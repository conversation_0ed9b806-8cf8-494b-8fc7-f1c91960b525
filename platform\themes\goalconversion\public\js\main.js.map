{"version": 3, "file": "/themes/goalconversion/js/main.js", "mappings": ";;;;;;;;;AAAA,CAAC,UAAUA,CAAC,EAAE;EACZA,CAAC,CAACC,QAAQ,CAAC,CAACC,KAAK,CAAC,YAAY;IAAA,IAAAC,QAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,UAAA;IAE9B;;IAEA;IACD;IACAX,CAAC,CAAC,kBAAkB,CAAC,CAACY,EAAE,CAAC,OAAO,EAAE,YAAY;MAC7CZ,CAAC,CAAC,gBAAgB,CAAC,CAACa,QAAQ,CAAC,eAAe,CAAC;IAC9C,CAAC,CAAC;IAEFb,CAAC,CAAC,mBAAmB,CAAC,CAACY,EAAE,CAAC,OAAO,EAAE,YAAY;MAC9CZ,CAAC,CAAC,gBAAgB,CAAC,CAACc,WAAW,CAAC,eAAe,CAAC;IACjD,CAAC,CAAC;;IAGD;IACD,IAAIC,QAAQ,GAAGf,CAAC,CAACgB,MAAM,CAAC;IACxBD,QAAQ,CAACH,EAAE,CAAC,QAAQ,EAAE,YAAY;MAChC,IAAIK,MAAM,GAAGF,QAAQ,CAACG,SAAS,CAAC,CAAC;MACjC,IAAID,MAAM,GAAG,GAAG,EAAE;QAChBjB,CAAC,CAAC,mBAAmB,CAAC,CAACc,WAAW,CAAC,eAAe,CAAC;MACrD,CAAC,MAAM;QACLd,CAAC,CAAC,mBAAmB,CAAC,CAACa,QAAQ,CAAC,eAAe,CAAC;MAClD;IACF,CAAC,CAAC;;IAEH;;IAEA;IACAb,CAAC,CAAC,YAAY,CAAC,CAACmB,KAAK,CAAC,UAAUC,KAAK,EAAE;MACrCpB,CAAC,CAAC,mBAAmB,CAAC,CAACqB,WAAW,CAAC,QAAQ,CAAC;IAC9C,CAAC,CAAC;IAEFrB,CAAC,CAAC,YAAY,CAAC,CAACsB,MAAM,CAAC,YAAY;MACjC,IAAItB,CAAC,CAAC,IAAI,CAAC,CAACuB,EAAE,CAAC,UAAU,CAAC,EAAE;QAC1BvB,CAAC,CAAC,sBAAsB,CAAC,CAACwB,IAAI,CAAC,CAAC;QAChCxB,CAAC,CAAC,uBAAuB,CAAC,CAACyB,IAAI,CAAC,CAAC;MACnC,CAAC,MAAM;QACLzB,CAAC,CAAC,sBAAsB,CAAC,CAACyB,IAAI,CAAC,CAAC;QAChCzB,CAAC,CAAC,uBAAuB,CAAC,CAACwB,IAAI,CAAC,CAAC;MACnC;IACF,CAAC,CAAC;;IAEF;IACE,IAAIE,UAAU,GAAG1B,CAAC,CAAC,6BAA6B,CAAC,CAAC2B,KAAK,CAAC,CAAC;IACzD,IAAIC,UAAU,GAAG5B,CAAC,CAAC,wBAAwB,CAAC;IAC5C4B,UAAU,CAACC,MAAM,CAACH,UAAU,CAAC;IAC7B,IAAI1B,CAAC,CAAC4B,UAAU,CAAC,CAACE,IAAI,CAAC,0BAA0B,CAAC,CAACC,MAAM,IAAI,CAAC,EAAE;MAC9D/B,CAAC,CAAC4B,UAAU,CAAC,CAACE,IAAI,CAAC,0BAA0B,CAAC,CAACE,MAAM,CAAC,CAAC,CAACH,MAAM,CAAC,6EAA6E,CAAC;IAC/I;IAEA,IAAII,YAAY,GAAGjC,CAAC,CAAC,wGAAwG,CAAC;IAC9HA,CAAC,CAACiC,YAAY,CAAC,CAACrB,EAAE,CAAC,OAAO,EAAE,UAAUsB,CAAC,EAAE;MACvCC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;MACdA,CAAC,CAACG,cAAc,CAAC,CAAC;MAClB,IAAI,CAAErC,CAAC,CAAC,IAAI,CAAC,CAACgC,MAAM,CAAC,CAAC,CAACM,QAAQ,CAAC,QAAQ,CAAE,EAAE;QAC1CtC,CAAC,CAAC,IAAI,CAAC,CAACgC,MAAM,CAAC,CAAC,CAACnB,QAAQ,CAAC,QAAQ,CAAC;QACnCb,CAAC,CAAC,IAAI,CAAC,CAACuC,QAAQ,CAAC,0BAA0B,CAAC,CAACC,SAAS,CAAC,CAAC;MAC1D,CAAC,MAAM;QACLxC,CAAC,CAAC,IAAI,CAAC,CAACuC,QAAQ,CAAC,0BAA0B,CAAC,CAACE,OAAO,CAAC,CAAC;QACtDzC,CAAC,CAAC,IAAI,CAAC,CAACgC,MAAM,CAAC,CAAC,CAAClB,WAAW,CAAC,QAAQ,CAAC;MACxC;IACF,CAAC,CAAC;IAGJd,CAAC,CAAC,sBAAsB,CAAC,CAACY,EAAE,CAAC,OAAO,EAAC,YAAU;MAC7CZ,CAAC,CAAC,eAAe,CAAC,CAACa,QAAQ,CAAC,mBAAmB,CAAC;MAChDb,CAAC,CAAC,uBAAuB,CAAC,CAACa,QAAQ,CAAC,2BAA2B,CAAC;IAClE,CAAC,CAAC;IAEFb,CAAC,CAAC,kDAAkD,CAAC,CAACY,EAAE,CAAC,OAAO,EAAE,YAAU;MAC1EZ,CAAC,CAAC,eAAe,CAAC,CAACc,WAAW,CAAC,mBAAmB,CAAC;MACnDd,CAAC,CAAC,uBAAuB,CAAC,CAACc,WAAW,CAAC,2BAA2B,CAAC;IACrE,CAAC,CAAC;;IAEF;;IAGI;MAAA,IACW4B,eAAe,GAAxB,SAASA,eAAeA,CAAA,EAAG;QACzB1C,CAAC,CAAC,cAAc,CAAC,CAAC2C,IAAI,CAAC,YAAY;UACjC,IAAIC,UAAU,GAAG5C,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC,CAACC,GAAG;UACrC,IAAIC,WAAW,GAAG/C,CAAC,CAACgB,MAAM,CAAC,CAACE,SAAS,CAAC,CAAC;UACvC,IAAI8B,OAAO,GAAGhD,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,SAAS,CAAC,CAACmB,IAAI,CAAC,cAAc,CAAC;UAC1D,IAAIC,UAAU,GAAGC,QAAQ,CAACH,OAAO,EAAE,EAAE,CAAC,GAAGG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC;UAC1D,IAAIC,OAAO,GAAGpD,CAAC,CAAC,IAAI,CAAC,CAACqD,IAAI,CAAC,SAAS,CAAC;UACrC,IAAIT,UAAU,GAAGG,WAAW,GAAG/C,CAAC,CAACgB,MAAM,CAAC,CAACsC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,CAACF,OAAO,EAAE;YAClEpD,CAAC,CAAC,IAAI,CAAC,CAACqD,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC;YAC7BrD,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,SAAS,CAAC,CAACyB,cAAc,CAAC;cACrCC,UAAU,EAAE,CAACC,IAAI,CAACC,EAAE,GAAG,CAAC;cACxBC,KAAK,EAAEX,OAAO,GAAG,GAAG;cACpBY,IAAI,EAAE,EAAE;cACRC,SAAS,EAAE,CAAC;cACZC,SAAS,EAAE,SAAS;cACpBC,IAAI,EAAE;gBACJC,KAAK,EAAE;cACT;YACF,CAAC,CAAC,CAACpD,EAAE,CAAC,2BAA2B,EAAE,UAAUQ,KAAK,EAAE6C,QAAQ,EAAEC,SAAS,EAAE;cACvElE,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,KAAK,CAAC,CAACqC,IAAI,CAAC,CAACD,SAAS,GAAC,GAAG,EAAEE,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3D,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,EAED;MACA3B,eAAe,CAAC,CAAC;MACjB1C,CAAC,CAACgB,MAAM,CAAC,CAACC,MAAM,CAACyB,eAAe,CAAC;IACnC;IAAC;IACA;IACA,IAAI1C,CAAC,CAAC,SAAS,CAAC,CAAC+B,MAAM,GAAG,CAAC,EAAE;MAC5B/B,CAAC,CAACgB,MAAM,CAAC,CAACJ,EAAE,CAAC,QAAQ,EAAE,UAAUQ,KAAK,EAAE;QACtC,IAAIH,MAAM,GAAGjB,CAAC,CAACgB,MAAM,CAAC,CAACE,SAAS,CAAC,CAAC;QAClC,IAAID,MAAM,GAAG,CAAC,EAAE;UACdjB,CAAC,CAAC,SAAS,CAAC,CAACc,WAAW,CAAC,QAAQ,CAAC;QACpC,CAAC,MAAM;UACLd,CAAC,CAAC,SAAS,CAAC,CAACa,QAAQ,CAAC,QAAQ,CAAC;QACjC;MACF,CAAC,CAAC;IACJ;;IAEM;IACEyD,GAAG,CAACC,IAAI,CAAC;MACP1B,MAAM,EAAE,CAAC;MACT2B,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAE,aAAa;MACrBC,eAAe,EAAE,YAAY;MAC7BC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE;IACR,CAAC,CAAC;;IAGF;IACA,IAAI5E,CAAC,CAAC,WAAW,CAAC,CAAC+B,MAAM,GAAG,CAAC,EAAE;MAC7B/B,CAAC,CAAC,WAAW,CAAC,CAAC6E,aAAa,CAAC;QAC3BC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IAAC;;IAEC;IACA,IAAIC,YAAY,GAAG9E,QAAQ,CAAC+E,aAAa,CAAC,qBAAqB,CAAC;IAChE,IAAIC,UAAU,GAAGF,YAAY,CAACG,cAAc,CAAC,CAAC;IAC9CH,YAAY,CAACI,KAAK,CAACC,UAAU,GAAGL,YAAY,CAACI,KAAK,CAACE,gBAAgB,GACjE,MAAM;IACRN,YAAY,CAACI,KAAK,CAACG,eAAe,GAAGL,UAAU,GAAG,GAAG,GAAGA,UAAU;IAClEF,YAAY,CAACI,KAAK,CAACI,gBAAgB,GAAGN,UAAU;IAChDF,YAAY,CAACS,qBAAqB,CAAC,CAAC;IACpCT,YAAY,CAACI,KAAK,CAACC,UAAU,GAAGL,YAAY,CAACI,KAAK,CAACE,gBAAgB,GACjE,+BAA+B;IACjC,IAAII,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAe;MAC/B,IAAIxE,MAAM,GAAGjB,CAAC,CAACgB,MAAM,CAAC,CAACE,SAAS,CAAC,CAAC;MAClC,IAAIoC,MAAM,GAAGtD,CAAC,CAACC,QAAQ,CAAC,CAACqD,MAAM,CAAC,CAAC,GAAGtD,CAAC,CAACgB,MAAM,CAAC,CAACsC,MAAM,CAAC,CAAC;MACtD,IAAIW,QAAQ,GAAGgB,UAAU,GAAIhE,MAAM,GAAGgE,UAAU,GAAI3B,MAAM;MAC1DyB,YAAY,CAACI,KAAK,CAACI,gBAAgB,GAAGtB,QAAQ;IAChD,CAAC;IACDwB,cAAc,CAAC,CAAC;IAChBzF,CAAC,CAACgB,MAAM,CAAC,CAACC,MAAM,CAACwE,cAAc,CAAC;IAChC,IAAI5C,MAAM,GAAG,EAAE;IACf,IAAI2B,QAAQ,GAAG,GAAG;IAClBkB,MAAM,CAAC1E,MAAM,CAAC,CAACJ,EAAE,CAAC,QAAQ,EAAE,YAAY;MACtC,IAAI8E,MAAM,CAAC,IAAI,CAAC,CAACxE,SAAS,CAAC,CAAC,GAAG2B,MAAM,EAAE;QACrC6C,MAAM,CAAC,gBAAgB,CAAC,CAAC7E,QAAQ,CAAC,iBAAiB,CAAC;MACtD,CAAC,MAAM;QACL6E,MAAM,CAAC,gBAAgB,CAAC,CAAC5E,WAAW,CAAC,iBAAiB,CAAC;MACzD;IACF,CAAC,CAAC;IACF4E,MAAM,CAAC,gBAAgB,CAAC,CAAC9E,EAAE,CAAC,OAAO,EAAE,UAAUQ,KAAK,EAAE;MACpDA,KAAK,CAACiB,cAAc,CAAC,CAAC;MACtBqD,MAAM,CAAC,YAAY,CAAC,CAACtC,OAAO,CAAC;QAAElC,SAAS,EAAE;MAAE,CAAC,EAAEsD,QAAQ,CAAC;MACxD,OAAO,KAAK;IACd,CAAC,CAAC;;IAEF;IACA,IAAMmB,MAAM,GAAG3F,CAAC,CAAC,6BAA6B,CAAC;IAC/C2F,MAAM,CAAC/E,EAAE,CAAC,OAAO,EAAE,YAAY;MAC7BZ,CAAC,CAAC,6BAA6B,CAAC,CAACc,WAAW,CAAC,QAAQ,CAAC;MACtDd,CAAC,CAAC,IAAI,CAAC,CAACa,QAAQ,CAAC,QAAQ,CAAC;IAC5B,CAAC,CAAC;;IAEF;IACA,IAAM+E,OAAO,GAAG5F,CAAC,CAAC,6BAA6B,CAAC;IAChD4F,OAAO,CAAChF,EAAE,CAAC,OAAO,EAAE,YAAY;MAC9BZ,CAAC,CAAC,6BAA6B,CAAC,CAACc,WAAW,CAAC,QAAQ,CAAC;MACtDd,CAAC,CAAC,IAAI,CAAC,CAACa,QAAQ,CAAC,QAAQ,CAAC;IAC5B,CAAC,CAAC;;IAEF;IACA,IAAMgF,OAAO,GAAG7F,CAAC,CAAC,SAAS,CAAC;IAC5B6F,OAAO,CAACjF,EAAE,CAAC,OAAO,EAAE,YAAY;MAC9BZ,CAAC,CAAC,SAAS,CAAC,CAACc,WAAW,CAAC,QAAQ,CAAC;MAClCd,CAAC,CAAC,IAAI,CAAC,CAACa,QAAQ,CAAC,QAAQ,CAAC;IAC5B,CAAC,CAAC;;IAEF;IACA,IAAMiF,IAAI,GAAG9F,CAAC,CAAC,cAAc,CAAC;IAC9B8F,IAAI,CAAClF,EAAE,CAAC,OAAO,EAAE,YAAY;MAC3BZ,CAAC,CAAC,cAAc,CAAC,CAACc,WAAW,CAAC,QAAQ,CAAC;MACvCd,CAAC,CAAC,IAAI,CAAC,CAACa,QAAQ,CAAC,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEFb,CAAC,CAAC,aAAa,CAAC,CAACmB,KAAK,CAAC,UAAUC,KAAK,EAAE;MACtCpB,CAAC,CAAC,oBAAoB,CAAC,CAACqB,WAAW,CAAC,QAAQ,CAAC;IAC/C,CAAC,CAAC;IAEFrB,CAAC,CAAC,aAAa,CAAC,CAACsB,MAAM,CAAC,YAAY;MAClC,IAAItB,CAAC,CAAC,IAAI,CAAC,CAACuB,EAAE,CAAC,UAAU,CAAC,EAAE;QAC1BvB,CAAC,CAAC,uBAAuB,CAAC,CAACwB,IAAI,CAAC,CAAC;QACjCxB,CAAC,CAAC,wBAAwB,CAAC,CAACyB,IAAI,CAAC,CAAC;MACpC,CAAC,MAAM;QACLzB,CAAC,CAAC,uBAAuB,CAAC,CAACyB,IAAI,CAAC,CAAC;QACjCzB,CAAC,CAAC,wBAAwB,CAAC,CAACwB,IAAI,CAAC,CAAC;MACpC;IACF,CAAC,CAAC;IAGZxB,CAAC,CAAC,eAAe,CAAC,CAAC+F,KAAK,CAAC;MACvBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAC,KAAK;MACdC,aAAa,EAAC,IAAI;MAClBC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAExG,CAAC,CAAC,mBAAmB,CAAC;MACjCyG,SAAS,EAAEzG,CAAC,CAAC,mBAAmB;IAClC,CAAC,CAAC;;IAEF;IACAA,CAAC,CAAC,cAAc,CAAC,CAAC+F,KAAK,CAAC;MACtBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAC,KAAK;MACdC,aAAa,EAAC,IAAI;MAClBC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAExG,CAAC,CAAC,kBAAkB,CAAC;MAChCyG,SAAS,EAAEzG,CAAC,CAAC,kBAAkB;IACjC,CAAC,CAAC;;IAED;IACAA,CAAC,CAAC,cAAc,CAAC,CAAC+F,KAAK,CAAC;MACvBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAC,KAAK;MACdC,aAAa,EAAC,IAAI;MAClBC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAExG,CAAC,CAAC,kBAAkB,CAAC;MAChCyG,SAAS,EAAEzG,CAAC,CAAC,kBAAkB;IACjC,CAAC,CAAC;;IAEF;IACAA,CAAC,CAAC,cAAc,CAAC,CAAC+F,KAAK,CAAC;MACtBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,MAAM,EAAE,KAAK;MACbQ,IAAI,EAAE,IAAI;MACVP,QAAQ,EAAC,KAAK;MACdC,aAAa,EAAC,IAAI;MAClBC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAExG,CAAC,CAAC,kBAAkB,CAAC;MAChCyG,SAAS,EAAEzG,CAAC,CAAC,kBAAkB;IACjC,CAAC,CAAC;;IAEF;IACAA,CAAC,CAAC,cAAc,CAAC,CAAC+F,KAAK,CAAC;MACtBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,MAAM,EAAE,IAAI;MACZQ,IAAI,EAAE,KAAK;MACXP,QAAQ,EAAC,KAAK;MACdC,aAAa,EAAC,IAAI;MAClBC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAExG,CAAC,CAAC,kBAAkB,CAAC;MAChCyG,SAAS,EAAEzG,CAAC,CAAC,kBAAkB,CAAC;MAChC2G,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA3G,CAAC,CAAC,cAAc,CAAC,CAAC+F,KAAK,CAAC;MACtBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,MAAM,EAAE,IAAI;MACZQ,IAAI,EAAE,KAAK;MACXP,QAAQ,EAAC,KAAK;MACdC,aAAa,EAAC,IAAI;MAClBC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAExG,CAAC,CAAC,kBAAkB,CAAC;MAChCyG,SAAS,EAAEzG,CAAC,CAAC,kBAAkB,CAAC;MAChC2G,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAGF;IACA3G,CAAC,CAAC,cAAc,CAAC,CAAC+F,KAAK,EAAA5F,QAAA;MACrByG,MAAM,EAAE,IAAI;MACZZ,YAAY,EAAE,CAAC;MACfE,MAAM,EAAE,KAAK;MACbW,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;IAAI,GAAAS,eAAA,CAAA3G,QAAA,gBACE,KAAK,GAAA2G,eAAA,CAAA3G,QAAA,eACN,IAAI,GAAA2G,eAAA,CAAA3G,QAAA,cACL,IAAI,GAAA2G,eAAA,CAAA3G,QAAA,mBACC,IAAI,GAAA2G,eAAA,CAAA3G,QAAA,UACb,KAAK,GAAA2G,eAAA,CAAA3G,QAAA,eACA,IAAI,GAAA2G,eAAA,CAAA3G,QAAA,gBACH,CACV;MACE4G,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEe,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,CACF,GAAA7F,QAAA,CACF,CAAC;;IAEF;IACAH,CAAC,CAAC,gBAAgB,CAAC,CAAC+F,KAAK,EAAA3F,SAAA;MACvBwG,MAAM,EAAE,IAAI;MACZZ,YAAY,EAAE,CAAC;MACfE,MAAM,EAAE,IAAI;MACZM,SAAS,EAAExG,CAAC,CAAC,oBAAoB,CAAC;MAClCyG,SAAS,EAAEzG,CAAC,CAAC,oBAAoB,CAAC;MAClC6G,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;IAAI,GAAAS,eAAA,CAAA1G,SAAA,gBACE,KAAK,GAAA0G,eAAA,CAAA1G,SAAA,eACN,IAAI,GAAA0G,eAAA,CAAA1G,SAAA,cACL,IAAI,GAAA0G,eAAA,CAAA1G,SAAA,mBACC,IAAI,GAAA0G,eAAA,CAAA1G,SAAA,UACb,KAAK,GAAA0G,eAAA,CAAA1G,SAAA,eACA,IAAI,GAAA0G,eAAA,CAAA1G,SAAA,gBACH,CACV;MACE2G,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEe,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,CACF,GAAA5F,SAAA,CACF,CAAC;;IAEF;IACAJ,CAAC,CAAC,eAAe,CAAC,CAAC+F,KAAK,EAAA1F,SAAA;MACtBuG,MAAM,EAAE,IAAI;MACZZ,YAAY,EAAE,CAAC;MACfE,MAAM,EAAE,IAAI;MACZM,SAAS,EAAExG,CAAC,CAAC,mBAAmB,CAAC;MACjCyG,SAAS,EAAEzG,CAAC,CAAC,mBAAmB,CAAC;MACjC6G,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;IAAI,GAAAS,eAAA,CAAAzG,SAAA,gBACE,KAAK,GAAAyG,eAAA,CAAAzG,SAAA,eACN,IAAI,GAAAyG,eAAA,CAAAzG,SAAA,cACL,IAAI,GAAAyG,eAAA,CAAAzG,SAAA,mBACC,IAAI,GAAAyG,eAAA,CAAAzG,SAAA,UACb,KAAK,GAAAyG,eAAA,CAAAzG,SAAA,eACA,IAAI,GAAAyG,eAAA,CAAAzG,SAAA,gBACH,CACV;MACE0G,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEe,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,CACF,GAAA3F,SAAA,CACF,CAAC;;IAEF;IACAL,CAAC,CAAC,eAAe,CAAC,CAAC+F,KAAK,EAAAzF,SAAA;MACtBsG,MAAM,EAAE,IAAI;MACZZ,YAAY,EAAE,CAAC;MACfE,MAAM,EAAE,IAAI;MACZM,SAAS,EAAExG,CAAC,CAAC,mBAAmB,CAAC;MACjCyG,SAAS,EAAEzG,CAAC,CAAC,mBAAmB,CAAC;MACjC6G,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;IAAI,GAAAS,eAAA,CAAAxG,SAAA,gBACE,KAAK,GAAAwG,eAAA,CAAAxG,SAAA,eACN,IAAI,GAAAwG,eAAA,CAAAxG,SAAA,cACL,IAAI,GAAAwG,eAAA,CAAAxG,SAAA,mBACC,IAAI,GAAAwG,eAAA,CAAAxG,SAAA,UACb,KAAK,GAAAwG,eAAA,CAAAxG,SAAA,eACA,IAAI,GAAAwG,eAAA,CAAAxG,SAAA,gBACH,CACV;MACEyG,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEe,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,CACF,GAAA1F,SAAA,CACF,CAAC;;IAEF;IACAN,CAAC,CAAC,iBAAiB,CAAC,CAAC+F,KAAK,EAAAxF,SAAA;MACxBqG,MAAM,EAAE,IAAI;MACZZ,YAAY,EAAE,CAAC;MACfE,MAAM,EAAE,KAAK;MACbW,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;IAAI,GAAAS,eAAA,CAAAvG,SAAA,gBACE,KAAK,GAAAuG,eAAA,CAAAvG,SAAA,eACN,IAAI,GAAAuG,eAAA,CAAAvG,SAAA,cACL,IAAI,GAAAuG,eAAA,CAAAvG,SAAA,mBACC,IAAI,GAAAuG,eAAA,CAAAvG,SAAA,UACb,KAAK,GAAAuG,eAAA,CAAAvG,SAAA,eACA,IAAI,GAAAuG,eAAA,CAAAvG,SAAA,gBACH,CACV;MACEwG,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEe,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,CACF,GAAAzF,SAAA,CACF,CAAC;;IAEF;IACAP,CAAC,CAAC,cAAc,CAAC,CAAC+F,KAAK,EAAAvF,SAAA;MACrBoG,MAAM,EAAE,IAAI;MACZZ,YAAY,EAAE,CAAC;MACfE,MAAM,EAAE,KAAK;MACbW,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,IAAI;MACVL,IAAI,EAAE;IAAI,GAAAS,eAAA,CAAAtG,SAAA,gBACE,KAAK,GAAAsG,eAAA,CAAAtG,SAAA,eACN,IAAI,GAAAsG,eAAA,CAAAtG,SAAA,cACL,IAAI,GAAAsG,eAAA,CAAAtG,SAAA,mBACC,IAAI,GAAAsG,eAAA,CAAAtG,SAAA,UACb,KAAK,GAAAsG,eAAA,CAAAtG,SAAA,eACA,IAAI,GAAAsG,eAAA,CAAAtG,SAAA,gBACH,CACV;MACEuG,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEe,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,CACF,GAAAxF,SAAA,CACF,CAAC;IAGFR,CAAC,CAAC,wBAAwB,CAAC,CAACkH,WAAW,CAAC;MACtCb,IAAI,EAAC,IAAI;MACTO,MAAM,EAAC,EAAE;MACTO,eAAe,EAAC,IAAI;MACpBC,GAAG,EAAE,KAAK;MACVC,UAAU,EAAC;QACP,CAAC,EAAC;UACEC,KAAK,EAAC,CAAC;UACPF,GAAG,EAAC;QACR,CAAC;QACD,GAAG,EAAC;UACAE,KAAK,EAAC,CAAC;UACPF,GAAG,EAAC;QACR,CAAC;QACD,IAAI,EAAC;UACDE,KAAK,EAAC,GAAG;UACTF,GAAG,EAAC,KAAK;UACTf,IAAI,EAAC;QACT;MACJ;IACJ,CAAC,CAAC;IAEFrG,CAAC,CAAC,eAAe,CAAC,CAACkH,WAAW,CAAC;MAC7Bb,IAAI,EAAC,IAAI;MACTO,MAAM,EAAC,EAAE;MACTO,eAAe,EAAC,IAAI;MACpBC,GAAG,EAAE,KAAK;MACVC,UAAU,EAAC;QACP,CAAC,EAAC;UACEC,KAAK,EAAC,CAAC;UACPF,GAAG,EAAC;QACR,CAAC;QACD,GAAG,EAAC;UACAE,KAAK,EAAC,CAAC;UACPF,GAAG,EAAC;QACR,CAAC;QACD,IAAI,EAAC;UACDE,KAAK,EAAC,CAAC;UACPF,GAAG,EAAC,KAAK;UACTf,IAAI,EAAC;QACT;MACJ;IACJ,CAAC,CAAC;;IAGA;IACArG,CAAC,CAAC,cAAc,CAAC,CAAC+F,KAAK,EAAAtF,SAAA;MACrBmG,MAAM,EAAE,IAAI;MACZZ,YAAY,EAAE,CAAC;MACfE,MAAM,EAAE,KAAK;MACbW,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;IAAI,GAAAS,eAAA,CAAArG,SAAA,gBACE,KAAK,GAAAqG,eAAA,CAAArG,SAAA,eACN,IAAI,GAAAqG,eAAA,CAAArG,SAAA,cACL,IAAI,GAAAqG,eAAA,CAAArG,SAAA,mBACC,IAAI,GAAAqG,eAAA,CAAArG,SAAA,UACb,KAAK,GAAAqG,eAAA,CAAArG,SAAA,eACA,IAAI,GAAAqG,eAAA,CAAArG,SAAA,UACT,IAAI,GAAAqG,eAAA,CAAArG,SAAA,gBACE,CACV;MACEsG,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEe,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,CACF,GAAAvF,SAAA,CACF,CAAC;;IAEF;IACAT,CAAC,CAAC,eAAe,CAAC,CAAC+F,KAAK,EAAArF,SAAA;MACtBkG,MAAM,EAAE,IAAI;MACZZ,YAAY,EAAE,CAAC;MACfE,MAAM,EAAE,KAAK;MACbW,UAAU,EAAE,IAAI;MAChBH,IAAI,EAAE,KAAK;MACXL,IAAI,EAAE;IAAI,GAAAS,eAAA,CAAApG,SAAA,gBACE,KAAK,GAAAoG,eAAA,CAAApG,SAAA,eACN,IAAI,GAAAoG,eAAA,CAAApG,SAAA,cACL,IAAI,GAAAoG,eAAA,CAAApG,SAAA,mBACC,IAAI,GAAAoG,eAAA,CAAApG,SAAA,UACb,KAAK,GAAAoG,eAAA,CAAApG,SAAA,eACA,IAAI,GAAAoG,eAAA,CAAApG,SAAA,UACT,IAAI,GAAAoG,eAAA,CAAApG,SAAA,gBACE,CACV;MACEqG,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,EACD;MACEe,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACRd,MAAM,EAAE,KAAK;QACbW,UAAU,EAAE,KAAK;QACjBI,aAAa,EAAE,MAAM;QACrBjB,YAAY,EAAE;MAChB;IACF,CAAC,CACF,GAAAtF,SAAA,CACF,CAAC;;IAGJ;IACA,IAAI6G,GAAG,GAAGvH,CAAC,CAAC,aAAa,CAAC;IAC1BuH,GAAG,CAAC3G,EAAE,CAAC,MAAM,EAAE,UAASQ,KAAK,EAAE2E,KAAK,EAAEyB,YAAY,EAAE;MAClD,IACEC,GAAG,GAAGzH,CAAC,CAAC+F,KAAK,CAAC2B,OAAO,CAAC3B,KAAK,CAACyB,YAAY,CAAC,CAAC;QAC1CG,IAAI,GAAGF,GAAG,CAACE,IAAI,CAAC,CAAC;QACjBC,IAAI,GAAGH,GAAG,CAACG,IAAI,CAAC,CAAC;MACnBA,IAAI,CAAC/G,QAAQ,CAAC,aAAa,CAAC;MAC5B8G,IAAI,CAAC9G,QAAQ,CAAC,aAAa,CAAC;MAC5B4G,GAAG,CAAC3G,WAAW,CAAC,aAAa,CAAC,CAACA,WAAW,CAAC,aAAa,CAAC;MACzDiF,KAAK,CAAC8B,KAAK,GAAGD,IAAI;MAClB7B,KAAK,CAAC+B,KAAK,GAAGH,IAAI;IACpB,CAAC,CAAC,CAAC/G,EAAE,CAAC,cAAc,EAAE,UAASQ,KAAK,EAAE2E,KAAK,EAAEyB,YAAY,EAAEO,SAAS,EAAE;MAEpE,IACEN,GAAG,GAAGzH,CAAC,CAAC+F,KAAK,CAAC2B,OAAO,CAACK,SAAS,CAAC,CAAC;MAEnChC,KAAK,CAAC8B,KAAK,CAAC/G,WAAW,CAAC,aAAa,CAAC;MACtCiF,KAAK,CAAC+B,KAAK,CAAChH,WAAW,CAAC,aAAa,CAAC;MACtC6G,IAAI,GAAGF,GAAG,CAACE,IAAI,CAAC,CAAC,EACfC,IAAI,GAAGH,GAAG,CAACG,IAAI,CAAC,CAAC;MACnBA,IAAI,CAACA,IAAI,CAAC,CAAC;MACXA,IAAI,CAACD,IAAI,CAAC,CAAC;MACXC,IAAI,CAAC/G,QAAQ,CAAC,aAAa,CAAC;MAC5B8G,IAAI,CAAC9G,QAAQ,CAAC,aAAa,CAAC;MAC5BkF,KAAK,CAAC8B,KAAK,GAAGD,IAAI;MAClB7B,KAAK,CAAC+B,KAAK,GAAGH,IAAI;MAClBF,GAAG,CAAC3G,WAAW,CAAC,YAAY,CAAC,CAACA,WAAW,CAAC,aAAa,CAAC;IAC1D,CAAC,CAAC;IAEFyG,GAAG,CAACxB,KAAK,EAAApF,UAAA;MACPqH,KAAK,EAAE,IAAI;MACX9B,MAAM,EAAE,IAAI;MACZQ,IAAI,EAAE,KAAK;MACXJ,aAAa,EAAE,IAAI;MACnBE,SAAS,EAAE,2EAA2E;MACtFC,SAAS,EAAE,6EAA6E;MACxFF,QAAQ,EAAE,IAAI;MACdM,UAAU,EAAE,IAAI;MAChBoB,YAAY,EAAE,CAAC;MACfjC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBgB,aAAa,EAAE,GAAG;MAClBiB,KAAK,EAAE,IAAI;MACX9B,aAAa,EAAC;IAAI,GAAAU,eAAA,CAAAnG,UAAA,WACZ,IAAI,GAAAmG,eAAA,CAAAnG,UAAA,cACD,KAAK,GAAAmG,eAAA,CAAAnG,UAAA,kBACA,SAAAwH,aAASC,MAAM,EAAEC,CAAC,EAAE;MAChC,OAAO,EAAE;IACX,CAAC,GAAA1H,UAAA,CAEF,CAAC;IAGAX,CAAC,CAAC,kBAAkB,CAAC,CAACsI,KAAK,CAAC,YAAY;MACtCtI,CAAC,CAAC,IAAI,CAAC,CAACa,QAAQ,CAAC,QAAQ,CAAC,CAAC0B,QAAQ,CAAC,CAAC,CAACzB,WAAW,CAAC,QAAQ,CAAC;IAC7D,CAAC,CAAC;EAEF,CAAC,CAAC;;EAIC;EACJ,IAAId,CAAC,CAAC,qBAAqB,CAAC,CAAC+B,MAAM,EAAE;IACpC,IAAIwG,aAAa,GAAI,IAAI;MACxBC,eAAe,GAAG,CAAC;MACnBC,UAAU,GAAK,GAAG;MAChBC,oBAAoB,GAAGzI,QAAQ,CAAC0I,gBAAgB,CAAC,qBAAqB,CAAC;IAE1ED,oBAAoB,CAACE,OAAO,CAAC,UAACC,OAAO,EAAK;MACzC,IAAIC,kBAAkB,GAAG,IAAIC,SAAS,CAACF,OAAO,EAAE;QAAE/D,IAAI,EAAE;MAAe,CAAC,CAAC;MACxEkE,IAAI,CAACC,IAAI,CAACH,kBAAkB,CAACI,KAAK,EAAE;QACpC1E,QAAQ,EAAE,CAAC;QACX2E,KAAK,EAAEV,UAAU;QACjBW,CAAC,EAAE,EAAE;QACLC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAEf,aAAa;QACtBgB,aAAa,EAAE;UAAEC,OAAO,EAAEX,OAAO;UAAEY,KAAK,EAAE;QAAU;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;EACH;EAEA,IAAIzJ,CAAC,CAAC,qBAAqB,CAAC,CAAC+B,MAAM,EAAE;IACpC,IAAKwG,cAAa,GAAK,IAAI;MACzBC,gBAAe,GAAG,EAAE;MACpBC,WAAU,GAAK,GAAG;MAClBiB,QAAQ,GAAM,YAAY;MAC1BhB,qBAAoB,GAAGzI,QAAQ,CAAC0I,gBAAgB,CAAC,qBAAqB,CAAC;IAEzED,qBAAoB,CAACE,OAAO,CAAC,UAACC,OAAO,EAAK;MACzC,IAAIC,kBAAkB,GAAG,IAAIC,SAAS,CAACF,OAAO,EAAE;QAAE/D,IAAI,EAAE;MAAe,CAAC,CAAC;MACxEkE,IAAI,CAACC,IAAI,CAACH,kBAAkB,CAACa,KAAK,EAAE;QACnCnF,QAAQ,EAAE,CAAC;QACX2E,KAAK,EAAEV,WAAU;QACjBW,CAAC,EAAEZ,gBAAe;QAClBa,SAAS,EAAE,CAAC;QACZC,OAAO,EAAEf,cAAa;QACtBqB,IAAI,EAAEF,QAAQ;QACdH,aAAa,EAAE;UAAEC,OAAO,EAAEX,OAAO;UAAEY,KAAK,EAAE;QAAS;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;EACH;EAEA,IAAIzJ,CAAC,CAAC,qBAAqB,CAAC,CAAC+B,MAAM,EAAE;IACpC,IAAI2G,sBAAoB,GAAGzI,QAAQ,CAAC0I,gBAAgB,CAAC,qBAAqB,CAAC;IAE1ED,sBAAoB,CAACE,OAAO,CAAC,UAACC,OAAO,EAAK;MAC1C;MACA,IAAIA,OAAO,CAACgB,SAAS,EAAE;QACtBhB,OAAO,CAACgB,SAAS,CAAC5F,QAAQ,CAAC,CAAC,CAAC,CAAC6F,IAAI,CAAC,CAAC;QACpCjB,OAAO,CAACkB,KAAK,CAACC,MAAM,CAAC,CAAC;MACvB;MAEAnB,OAAO,CAACkB,KAAK,GAAG,IAAIhB,SAAS,CAACF,OAAO,EAAE;QACtC/D,IAAI,EAAE,mBAAmB;QACzBmF,UAAU,EAAE;MACb,CAAC,CAAC;MACFjB,IAAI,CAACkB,GAAG,CAACrB,OAAO,EAAE;QAAEsB,WAAW,EAAE;MAAI,CAAC,CAAC;MAEvCnB,IAAI,CAACkB,GAAG,CAACrB,OAAO,CAACkB,KAAK,CAACJ,KAAK,EAAE;QAC7BS,OAAO,EAAE,CAAC;QACVhB,CAAC,EAAE;MACJ,CAAC,CAAC;MAEFP,OAAO,CAACgB,SAAS,GAAGb,IAAI,CAACqB,EAAE,CAACxB,OAAO,CAACkB,KAAK,CAACJ,KAAK,EAAE;QAChDJ,aAAa,EAAE;UAAEC,OAAO,EAAEX,OAAO;UAAEY,KAAK,EAAE;QAAU,CAAC;QACrDL,CAAC,EAAE,GAAG;QACNkB,CAAC,EAAE,GAAG;QACNC,OAAO,EAAE,GAAG;QACZH,OAAO,EAAE,CAAC;QACV5F,QAAQ,EAAE,CAAC;QACXoF,IAAI,EAAEY,IAAI,CAACC,OAAO;QAClBnB,OAAO,EAAE;MACV,CAAC,CAAC;IACH,CAAC,CAAC;EACH;;EAGC;EACAtJ,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,aAAa,CAAC,CACbY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,aAAa,CAAC,CACbY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,aAAa,CAAC,CACbY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,aAAa,CAAC,CACbY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,aAAa,CAAC,CACbY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,aAAa,CAAC,CACbY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,aAAa,CAAC,CACbY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,cAAc,CAAC,CACdY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,cAAc,CAAC,CACdY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;;EAEF;EACA3K,CAAC,CAAC,YAAW;IACXA,CAAC,CAAC,cAAc,CAAC,CACdY,EAAE,CAAC,YAAY,EAAE,UAASsB,CAAC,EAAE;MACtB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACnC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACvD,CAAC,CAAC,CACD/J,EAAE,CAAC,UAAU,EAAE,UAASsB,CAAC,EAAE;MACpB,IAAIwI,YAAY,GAAG1K,CAAC,CAAC,IAAI,CAAC,CAAC6C,MAAM,CAAC,CAAC;QACjC8H,IAAI,GAAGzI,CAAC,CAAC0I,KAAK,GAAGF,YAAY,CAACG,IAAI;QAClCC,IAAI,GAAG5I,CAAC,CAAC6I,KAAK,GAAGL,YAAY,CAAC5H,GAAG;MACvC9C,CAAC,CAAC,IAAI,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC,CAACkJ,GAAG,CAAC;QAAClI,GAAG,EAACgI,IAAI;QAAED,IAAI,EAACF;MAAI,CAAC,CAAC;IACnD,CAAC,CAAC;EACN,CAAC,CAAC;EAIF3K,CAAC,CAAC,QAAQ,CAAC,CAACiL,UAAU,CAAC,CAAC;EAExBjL,CAAC,CAAC,UAAU,CAAC,CAAC+F,KAAK,CAAC;IAClBW,IAAI,EAAE,KAAK;IACXL,IAAI,EAAE,IAAI;IACVE,QAAQ,EAAE,IAAI;IACdyB,KAAK,EAAE,GAAG;IACVhC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBE,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,IAAI;IACnBiB,UAAU,EAAE,CACR;MACIN,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;QACNhB,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE,CAAC;QACjBM,QAAQ,EAAE,IAAI;QACdG,IAAI,EAAE;MACV;IACJ,CAAC,EACD;MACIK,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACNhB,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE;MACpB;IACJ,CAAC,EACD;MACIc,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;QACNhB,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE;MACpB;IACJ;IACA;IACA;IACA;IAAA;EAER,CAAC,CAAC;AAGF,CAAC,EAAEP,MAAM,CAAC;;AAGV;AACAzF,QAAQ,CAACiL,gBAAgB,CAAC,kBAAkB,EAAE,YAAY;EACxD,IAAIC,OAAO,GAAG,IAAIC,MAAM,CAAC,gBAAgB,EAAE;IACzCC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,CAAC;IAChBC,QAAQ,EAAE,IAAI;IACdC,mBAAmB,EAAE,IAAI;IACzBrF,QAAQ,EAAE;MACRgD,KAAK,EAAE,IAAI;MACXsC,oBAAoB,EAAE;IACxB;EACF,CAAC,CAAC;EACF,IAAIC,OAAO,GAAG,IAAIN,MAAM,CAAC,uBAAuB,EAAE;IAChDC,YAAY,EAAE,EAAE;IAChBhF,IAAI,EAAE,IAAI;IACVsF,UAAU,EAAE;MACVC,MAAM,EAAE,qBAAqB;MAC7BC,MAAM,EAAE;IACV,CAAC;IACD1F,QAAQ,EAAE;MACRgD,KAAK,EAAE,IAAI;MACXsC,oBAAoB,EAAE;IACxB,CAAC;IACDK,MAAM,EAAE;MACNC,MAAM,EAAEZ;IACV;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AAKFlL,QAAQ,CAACiL,gBAAgB,CAAC,kBAAkB,EAAE,YAAM;EAClD;EACA,IAAMc,UAAU,GAAG/L,QAAQ,CAAC0I,gBAAgB,CAAC,WAAW,CAAC;;EAEzD;EACA,IAAIqD,UAAU,CAACjK,MAAM,GAAG,CAAC,EAAE;IACzBiK,UAAU,CAACpD,OAAO,CAAC,UAACqD,QAAQ,EAAK;MAC/BA,QAAQ,CAACf,gBAAgB,CAAC,OAAO,EAAE,YAAM;QACvCc,UAAU,CAACpD,OAAO,CAAC,UAACsD,GAAG;UAAA,OAAKA,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;QAAA,EAAC;QAC3DH,QAAQ,CAACE,SAAS,CAACE,GAAG,CAAC,QAAQ,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AAEF,CAAC,CAAC;AAIF,IAAMjE,MAAM,GAAGnI,QAAQ,CAACqM,cAAc,CAAC,gBAAgB,CAAC;AACxD,IAAMC,aAAa,GAAGtM,QAAQ,CAACqM,cAAc,CAAC,eAAe,CAAC;AAE9D,IAAIlE,MAAM,IAAImE,aAAa,EAAE;EAC3B;EAAA,IACSC,sBAAsB,GAA/B,SAASA,sBAAsBA,CAAA,EAAG;IAChC,IAAM7I,KAAK,GAAGyE,MAAM,CAACzE,KAAK;IAC1B,IAAM8I,GAAG,GAAGrE,MAAM,CAACqE,GAAG;IACtB,IAAMvJ,UAAU,GAAIS,KAAK,GAAG8I,GAAG,GAAI,GAAG;IACtCrE,MAAM,CAACjD,KAAK,CAACuH,UAAU,qCAAAC,MAAA,CAAqCzJ,UAAU,iBAAAyJ,MAAA,CAAczJ,UAAU,OAAI;EACpG,CAAC,EAED;EACAkF,MAAM,CAAC8C,gBAAgB,CAAC,OAAO,EAAE,YAAY;IAC3CqB,aAAa,CAACK,WAAW,GAAG,IAAI,CAACjJ,KAAK;IACtC6I,sBAAsB,CAAC,CAAC;EAC1B,CAAC,CAAC;;EAEF;EACAA,sBAAsB,CAAC,CAAC;AAC1B,C", "sources": ["webpack:///./platform/themes/goalconversion/assets/js/main.js"], "sourcesContent": ["(function ($) {\n  $(document).ready(function () {\n\n  ////////////////////////////////////////////////////\n\n  ////////////////////////////////////////////////////\n\t// 03. Search Js\n\t$(\".search-open-btn\").on(\"click\", function () {\n\t\t$(\".search__popup\").addClass(\"search-opened\");\n\t});\n\t\t\n\t$(\".search-close-btn\").on(\"click\", function () {\n\t\t$(\".search__popup\").removeClass(\"search-opened\");\n\t});\n\n\n  //========== HEADER ACTIVE STRATS ============= //\n var windowOn = $(window);\n windowOn.on('scroll', function () {\n   var scroll = windowOn.scrollTop();\n   if (scroll < 100) {\n     $(\"#vl-header-sticky\").removeClass(\"header-sticky\");\n   } else {\n     $(\"#vl-header-sticky\").addClass(\"header-sticky\");\n   }\n });\n \n//========== HEADER ACTIVE ENDS ============= //\n\n//========== PRICING AREA ============= //\n$(\"#ce-toggle\").click(function (event) {\n  $(\".plan-toggle-wrap\").toggleClass(\"active\");\n});\n\n$(\"#ce-toggle\").change(function () {\n  if ($(this).is(\":checked\")) {\n    $(\".tab-content #yearly\").hide();\n    $(\".tab-content #monthly\").show();\n  } else {\n    $(\".tab-content #yearly\").show();\n    $(\".tab-content #monthly\").hide();\n  }\n});\n\n//========== MOBILE MENU STARTS ============= //\n  var vlMenuWrap = $('.vl-mobile-menu-active > ul').clone();\n  var vlSideMenu = $('.vl-offcanvas-menu nav');\n  vlSideMenu.append(vlMenuWrap);\n  if ($(vlSideMenu).find('.sub-menu, .vl-mega-menu').length != 0) {\n    $(vlSideMenu).find('.sub-menu, .vl-mega-menu').parent().append('<button class=\"vl-menu-close\"><i class=\"fas fa-chevron-right\"></i></button>');\n  }\n\n  var sideMenuList = $('.vl-offcanvas-menu nav > ul > li button.vl-menu-close, .vl-offcanvas-menu nav > ul li.has-dropdown > a');\n  $(sideMenuList).on('click', function (e) {\n    console.log(e);\n    e.preventDefault();\n    if (!($(this).parent().hasClass('active'))) {\n      $(this).parent().addClass('active');\n      $(this).siblings('.sub-menu, .vl-mega-menu').slideDown();\n    } else {\n      $(this).siblings('.sub-menu, .vl-mega-menu').slideUp();\n      $(this).parent().removeClass('active');\n    }\n  });\n\n\n$(\".vl-offcanvas-toggle\").on('click',function(){\n  $(\".vl-offcanvas\").addClass(\"vl-offcanvas-open\");\n  $(\".vl-offcanvas-overlay\").addClass(\"vl-offcanvas-overlay-open\");\n});\n\n$(\".vl-offcanvas-close-toggle,.vl-offcanvas-overlay\").on('click', function(){\n  $(\".vl-offcanvas\").removeClass(\"vl-offcanvas-open\");\n  $(\".vl-offcanvas-overlay\").removeClass(\"vl-offcanvas-overlay-open\");\n});\n\n//========== MOBILE MENU ENDS ============= //\n  \n\n    {\n      function animateElements() {\n        $('.progressbar').each(function () {\n          var elementPos = $(this).offset().top;\n          var topOfWindow = $(window).scrollTop();\n          var percent = $(this).find('.circle').attr('data-percent');\n          var percentage = parseInt(percent, 10) / parseInt(100, 10);\n          var animate = $(this).data('animate');\n          if (elementPos < topOfWindow + $(window).height() - 10 && !animate) {\n            $(this).data('animate', true);\n            $(this).find('.circle').circleProgress({\n              startAngle: -Math.PI / 2,\n              value: percent / 100,\n              size: 80,\n              thickness: 5,\n              emptyFill: \"#E7E6F1\",\n              fill: {\n                color: '#0778F9'\n              }\n            }).on('circle-animation-progress', function (event, progress, stepValue) {\n              $(this).find('div').text((stepValue*100).toFixed() + \"%\");\n            }).stop();\n          }\n        });\n      }\n    \n      // Show animated elements\n      animateElements();\n      $(window).scroll(animateElements);\n    };\n     // sticky header active\n     if ($(\"#header\").length > 0) {\n      $(window).on(\"scroll\", function (event) {\n        var scroll = $(window).scrollTop();\n        if (scroll < 1) {\n          $(\"#header\").removeClass(\"sticky\");\n        } else {\n          $(\"#header\").addClass(\"sticky\");\n        }\n      });\n    }\n\n          //Aos animation active\n            AOS.init({\n              offset: 0,\n              duration: 400,\n              easing: \"ease-in-out\",\n              anchorPlacement: \"top-bottom\",\n              disable: \"mobile\",\n              once: false,\n            });\n\n\n            //Video poppup\n            if ($(\".play-btn\").length > 0) {\n              $(\".play-btn\").magnificPopup({\n                type: \"iframe\",\n              });\n            };\n\n              // page-progress\n              var progressPath = document.querySelector(\".progress-wrap path\");\n              var pathLength = progressPath.getTotalLength();\n              progressPath.style.transition = progressPath.style.WebkitTransition =\n                \"none\";\n              progressPath.style.strokeDasharray = pathLength + \" \" + pathLength;\n              progressPath.style.strokeDashoffset = pathLength;\n              progressPath.getBoundingClientRect();\n              progressPath.style.transition = progressPath.style.WebkitTransition =\n                \"stroke-dashoffset 10ms linear\";\n              var updateProgress = function () {\n                var scroll = $(window).scrollTop();\n                var height = $(document).height() - $(window).height();\n                var progress = pathLength - (scroll * pathLength) / height;\n                progressPath.style.strokeDashoffset = progress;\n              };\n              updateProgress();\n              $(window).scroll(updateProgress);\n              var offset = 50;\n              var duration = 550;\n              jQuery(window).on(\"scroll\", function () {\n                if (jQuery(this).scrollTop() > offset) {\n                  jQuery(\".progress-wrap\").addClass(\"active-progress\");\n                } else {\n                  jQuery(\".progress-wrap\").removeClass(\"active-progress\");\n                }\n              });\n              jQuery(\".progress-wrap\").on(\"click\", function (event) {\n                event.preventDefault();\n                jQuery(\"html, body\").animate({ scrollTop: 0 }, duration);\n                return false;\n              });\n\n              //product colors\n              const colors = $(\".accordion1 .accordion-item\");\n              colors.on(\"click\", function () {\n                $(\".accordion1 .accordion-item\").removeClass(\"active\");\n                $(this).addClass(\"active\");\n              });\n\n              //product colors\n              const colors2 = $(\".accordion2 .accordion-item\");\n              colors2.on(\"click\", function () {\n                $(\".accordion2 .accordion-item\").removeClass(\"active\");\n                $(this).addClass(\"active\");\n              });\n\n              //select colors\n              const select1 = $(\"select1\");\n              select1.on(\"click\", function () {\n                $(\"select1\").removeClass(\"active\");\n                $(this).addClass(\"active\");\n              });\n\n              //tes1 active\n              const tes1 = $(\".controls li\");\n              tes1.on(\"click\", function () {\n                $(\".controls li\").removeClass(\"active\");\n                $(this).addClass(\"active\");\n              });\n\n              $(\"#ce-toggle1\").click(function (event) {\n                $(\".plan-toggle-wrap1\").toggleClass(\"active\");\n              });\n              \n              $(\"#ce-toggle1\").change(function () {\n                if ($(this).is(\":checked\")) {\n                  $(\".tab-content #yearly1\").hide();\n                  $(\".tab-content #monthly1\").show();\n                } else {\n                  $(\".tab-content #yearly1\").show();\n                  $(\".tab-content #monthly1\").hide();\n                }\n              });\n\n\n    $(\".work1-slider\").slick({\n      slidesToShow: 1,\n      slidesToScroll: 1,\n      arrows: true,\n      autoplay:false,\n      autoplaySpeed:2000,\n      loop: true,\n      focusOnSelect: true,\n      infinite: true,\n      prevArrow: $('.work1-prev-arrow'),\n      nextArrow: $('.work1-next-arrow'),\n    });\n\n    //-- testimonial 6 ---\n    $(\".tes6-slider\").slick({\n      slidesToShow: 1,\n      slidesToScroll: 1,\n      arrows: true,\n      autoplay:false,\n      autoplaySpeed:2000,\n      loop: true,\n      focusOnSelect: true,\n      infinite: true,\n      prevArrow: $('.tes6-prev-arrow'),\n      nextArrow: $('.tes6-next-arrow'),\n    });\n\n     //-- testimonial 6 ---\n     $(\".tes5-slider\").slick({\n      slidesToShow: 1,\n      slidesToScroll: 1,\n      arrows: true,\n      autoplay:false,\n      autoplaySpeed:2000,\n      loop: true,\n      focusOnSelect: true,\n      infinite: true,\n      prevArrow: $('.tes5-prev-arrow'),\n      nextArrow: $('.tes5-next-arrow'),\n    });\n\n    //-- testimonial 6 ---\n    $(\".tes4-slider\").slick({\n      slidesToShow: 1,\n      slidesToScroll: 1,\n      arrows: false,\n      dots: true,\n      autoplay:false,\n      autoplaySpeed:2000,\n      loop: true,\n      focusOnSelect: true,\n      infinite: true,\n      prevArrow: $('.tes5-prev-arrow'),\n      nextArrow: $('.tes5-next-arrow'),\n    });\n\n    //-- testimonial 9 ---\n    $(\".tes9-slider\").slick({\n      slidesToShow: 1,\n      slidesToScroll: 1,\n      arrows: true,\n      dots: false,\n      autoplay:false,\n      autoplaySpeed:2000,\n      loop: true,\n      focusOnSelect: true,\n      infinite: true,\n      prevArrow: $('.tes9-prev-arrow'),\n      nextArrow: $('.tes9-next-arrow'),\n      vertical: true,\n    });\n\n    //-- testimonial 9 ---\n    $(\".tes8-slider\").slick({\n      slidesToShow: 1,\n      slidesToScroll: 1,\n      arrows: true,\n      dots: false,\n      autoplay:false,\n      autoplaySpeed:2000,\n      loop: true,\n      focusOnSelect: true,\n      infinite: true,\n      prevArrow: $('.tes8-prev-arrow'),\n      nextArrow: $('.tes8-next-arrow'),\n      vertical: true,\n    });\n\n\n    //testimonial 2 slider\n    $(\".tes2-slider\").slick({\n      margin: \"30\",\n      slidesToShow: 3,\n      arrows: false,\n      centerMode: true,\n      dots: false,\n      loop: true,\n      centerMode: false,\n      draggable: true,\n      autoplay: true,\n      autoplaySpeed: 4000,\n      fade: false,\n      fadeSpeed: 1000,\n      responsive: [\n        {\n          breakpoint: 769,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n        {\n          breakpoint: 480,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n      ],\n    });\n\n    //Team 10 slider\n    $(\".team10-slider\").slick({\n      margin: \"30\",\n      slidesToShow: 3,\n      arrows: true,\n      prevArrow: $('.team10-prev-arrow'),\n      nextArrow: $('.team10-next-arrow'),\n      centerMode: true,\n      dots: false,\n      loop: true,\n      centerMode: false,\n      draggable: true,\n      autoplay: true,\n      autoplaySpeed: 4000,\n      fade: false,\n      fadeSpeed: 1000,\n      responsive: [\n        {\n          breakpoint: 769,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n        {\n          breakpoint: 480,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n      ],\n    });\n\n    //Team 8 slider\n    $(\".team8-slider\").slick({\n      margin: \"30\",\n      slidesToShow: 4,\n      arrows: true,\n      prevArrow: $('.team8-prev-arrow'),\n      nextArrow: $('.team8-next-arrow'),\n      centerMode: true,\n      dots: false,\n      loop: true,\n      centerMode: false,\n      draggable: true,\n      autoplay: true,\n      autoplaySpeed: 4000,\n      fade: false,\n      fadeSpeed: 1000,\n      responsive: [\n        {\n          breakpoint: 769,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n        {\n          breakpoint: 480,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n      ],\n    });\n\n    //Team 10 slider\n    $(\".tes10-slider\").slick({\n      margin: \"30\",\n      slidesToShow: 3,\n      arrows: true,\n      prevArrow: $('.tes10-prev-arrow'),\n      nextArrow: $('.tes10-next-arrow'),\n      centerMode: true,\n      dots: false,\n      loop: true,\n      centerMode: false,\n      draggable: true,\n      autoplay: true,\n      autoplaySpeed: 4000,\n      fade: false,\n      fadeSpeed: 1000,\n      responsive: [\n        {\n          breakpoint: 769,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n        {\n          breakpoint: 480,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n      ],\n    });\n\n    // brands slider 9\n    $(\".brands9-slider\").slick({\n      margin: \"30\",\n      slidesToShow: 7,\n      arrows: false,\n      centerMode: true,\n      dots: false,\n      loop: true,\n      centerMode: false,\n      draggable: true,\n      autoplay: true,\n      autoplaySpeed: 2000,\n      fade: false,\n      fadeSpeed: 1000,\n      responsive: [\n        {\n          breakpoint: 769,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 4,\n          },\n        },\n        {\n          breakpoint: 480,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 2,\n          },\n        },\n      ],\n    });\n\n    //testimonial 2 slider\n    $(\".tes7-slider\").slick({\n      margin: \"30\",\n      slidesToShow: 3,\n      arrows: false,\n      centerMode: true,\n      dots: true,\n      loop: true,\n      centerMode: false,\n      draggable: true,\n      autoplay: true,\n      autoplaySpeed: 4000,\n      fade: false,\n      fadeSpeed: 1000,\n      responsive: [\n        {\n          breakpoint: 769,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n        {\n          breakpoint: 480,\n          settings: {\n            arrows: false,\n            centerMode: false,\n            centerPadding: \"40px\",\n            slidesToShow: 1,\n          },\n        },\n      ],\n    });\n\n\n    $('.istagram-feed3-slider').owlCarousel({\n      loop:true,\n      margin:10,\n      responsiveClass:true,\n      nav: false,\n      responsive:{\n          0:{\n              items:1,\n              nav:false\n          },\n          600:{\n              items:3,\n              nav:false\n          },\n          1000:{\n              items:5.5,\n              nav:false,\n              loop:false\n          }\n      }\n  });\n\n  $('.hero7-slider').owlCarousel({\n    loop:true,\n    margin:10,\n    responsiveClass:true,\n    nav: false,\n    responsive:{\n        0:{\n            items:1,\n            nav:false\n        },\n        600:{\n            items:3,\n            nav:false\n        },\n        1000:{\n            items:5,\n            nav:false,\n            loop:false\n        }\n    }\n});\n\n\n  //testimonial 3 slider\n  $(\".tes3-slider\").slick({\n    margin: \"30\",\n    slidesToShow: 1,\n    arrows: false,\n    centerMode: true,\n    dots: false,\n    loop: true,\n    centerMode: false,\n    draggable: true,\n    autoplay: true,\n    autoplaySpeed: 4000,\n    fade: false,\n    fadeSpeed: 1000,\n    dots: true,\n    responsive: [\n      {\n        breakpoint: 769,\n        settings: {\n          arrows: false,\n          centerMode: false,\n          centerPadding: \"40px\",\n          slidesToShow: 1,\n        },\n      },\n      {\n        breakpoint: 480,\n        settings: {\n          arrows: false,\n          centerMode: false,\n          centerPadding: \"40px\",\n          slidesToShow: 1,\n        },\n      },\n    ],\n  });\n\n  //testimonial 11 slider\n  $(\".tes11-slider\").slick({\n    margin: \"30\",\n    slidesToShow: 1,\n    arrows: false,\n    centerMode: true,\n    dots: false,\n    loop: true,\n    centerMode: false,\n    draggable: true,\n    autoplay: true,\n    autoplaySpeed: 4000,\n    fade: false,\n    fadeSpeed: 1000,\n    dots: true,\n    responsive: [\n      {\n        breakpoint: 769,\n        settings: {\n          arrows: false,\n          centerMode: false,\n          centerPadding: \"40px\",\n          slidesToShow: 1,\n        },\n      },\n      {\n        breakpoint: 480,\n        settings: {\n          arrows: false,\n          centerMode: false,\n          centerPadding: \"40px\",\n          slidesToShow: 1,\n        },\n      },\n    ],\n  });\n\n\n// SLIDER //\nvar rev = $('.rev_slider');\nrev.on('init', function(event, slick, currentSlide) {\n  var\n    cur = $(slick.$slides[slick.currentSlide]),\n    next = cur.next(),\n    prev = cur.prev();\n  prev.addClass('slick-sprev');\n  next.addClass('slick-snext');\n  cur.removeClass('slick-snext').removeClass('slick-sprev');\n  slick.$prev = prev;\n  slick.$next = next;\n}).on('beforeChange', function(event, slick, currentSlide, nextSlide) {\n\n  var\n    cur = $(slick.$slides[nextSlide]);\n\n  slick.$prev.removeClass('slick-sprev');\n  slick.$next.removeClass('slick-snext');\n  next = cur.next(),\n    prev = cur.prev();\n  prev.prev();\n  prev.next();\n  prev.addClass('slick-sprev');\n  next.addClass('slick-snext');\n  slick.$prev = prev;\n  slick.$next = next;\n  cur.removeClass('slick-next').removeClass('slick-sprev');\n});\n\nrev.slick({\n  speed: 1000,\n  arrows: true,\n  dots: false,\n  focusOnSelect: true,\n  prevArrow: '<button class=\"prev-next\"><i class=\"fa-solid fa-angle-left\"></i></button>',\n  nextArrow: '<button class=\"next-prev\"> <i class=\"fa-solid fa-angle-right\"></i></button>',\n  infinite: true,\n  centerMode: true,\n  slidesPerRow: 1,\n  slidesToShow: 5,\n  slidesToScroll: 1,\n  centerPadding: '0',\n  swipe: true,\n  autoplaySpeed:2500,\n  speed:1500,\n  autoplay:false,\n  customPaging: function(slider, i) {\n    return '';\n  },\n\n});\n\n\n  $('.cs_hover_active').hover(function () {\n    $(this).addClass('active').siblings().removeClass('active');\n  });\n          \n  });\n\n\n\n    \t/* Text Effect Animation */\n\tif ($('.text-anime-style-1').length) {\n\t\tlet staggerAmount \t= 0.05,\n\t\t\ttranslateXValue = 0,\n\t\t\tdelayValue \t\t= 0.5,\n\t\t   animatedTextElements = document.querySelectorAll('.text-anime-style-1');\n\t\t\n\t\tanimatedTextElements.forEach((element) => {\n\t\t\tlet animationSplitText = new SplitText(element, { type: \"chars, words\" });\n\t\t\t\tgsap.from(animationSplitText.words, {\n\t\t\t\tduration: 1,\n\t\t\t\tdelay: delayValue,\n\t\t\t\tx: 20,\n\t\t\t\tautoAlpha: 0,\n\t\t\t\tstagger: staggerAmount,\n\t\t\t\tscrollTrigger: { trigger: element, start: \"top 85%\" },\n\t\t\t\t});\n\t\t});\t\t\n\t}\n\t\n\tif ($('.text-anime-style-2').length) {\t\t\t\t\n\t\tlet\t staggerAmount \t\t= 0.05,\n\t\t\t translateXValue\t= 20,\n\t\t\t delayValue \t\t= 0.5,\n\t\t\t easeType \t\t\t= \"power2.out\",\n\t\t\t animatedTextElements = document.querySelectorAll('.text-anime-style-2');\n\t\t\n\t\tanimatedTextElements.forEach((element) => {\n\t\t\tlet animationSplitText = new SplitText(element, { type: \"chars, words\" });\n\t\t\t\tgsap.from(animationSplitText.chars, {\n\t\t\t\t\tduration: 1,\n\t\t\t\t\tdelay: delayValue,\n\t\t\t\t\tx: translateXValue,\n\t\t\t\t\tautoAlpha: 0,\n\t\t\t\t\tstagger: staggerAmount,\n\t\t\t\t\tease: easeType,\n\t\t\t\t\tscrollTrigger: { trigger: element, start: \"top 85%\"},\n\t\t\t\t});\n\t\t});\t\t\n\t}\n\t\n\tif ($('.text-anime-style-3').length) {\t\t\n\t\tlet\tanimatedTextElements = document.querySelectorAll('.text-anime-style-3');\n\t\t\n\t\t animatedTextElements.forEach((element) => {\n\t\t\t//Reset if needed\n\t\t\tif (element.animation) {\n\t\t\t\telement.animation.progress(1).kill();\n\t\t\t\telement.split.revert();\n\t\t\t}\n\n\t\t\telement.split = new SplitText(element, {\n\t\t\t\ttype: \"lines,words,chars\",\n\t\t\t\tlinesClass: \"split-line\",\n\t\t\t});\n\t\t\tgsap.set(element, { perspective: 400 });\n\n\t\t\tgsap.set(element.split.chars, {\n\t\t\t\topacity: 0,\n\t\t\t\tx: \"50\",\n\t\t\t});\n\n\t\t\telement.animation = gsap.to(element.split.chars, {\n\t\t\t\tscrollTrigger: { trigger: element,\tstart: \"top 95%\" },\n\t\t\t\tx: \"0\",\n\t\t\t\ty: \"0\",\n\t\t\t\trotateX: \"0\",\n\t\t\t\topacity: 1,\n\t\t\t\tduration: 1,\n\t\t\t\tease: Back.easeOut,\n\t\t\t\tstagger: 0.02,\n\t\t\t});\n\t\t});\t\t\n\t}\n\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme3')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme4')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme5')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme6')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme7')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme8')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme9')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme10')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme11')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n  // btn_theme\n  $(function() {\n    $('.btn_theme12')\n      .on('mouseenter', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n              $(this).find('span').css({top:relY, left:relX})\n      })\n      .on('mouseout', function(e) {\n              var parentOffset = $(this).offset(),\n                relX = e.pageX - parentOffset.left,\n                relY = e.pageY - parentOffset.top;\n          $(this).find('span').css({top:relY, left:relX})\n      });\n  });\n\n\n\n  $('select').niceSelect();\n\n  $('.clients').slick({\n    dots: false,\n    loop: true,\n    infinite: true,\n    speed: 300,\n    slidesToShow: 8,\n    slidesToScroll: 2,\n    autoplay: true,\n    autoplaySpeed: 2000,\n    responsive: [\n        {\n            breakpoint: 1024,\n            settings: {\n                slidesToShow: 4,\n                slidesToScroll: 2,\n                infinite: true,\n                dots: false\n            }\n        },\n        {\n            breakpoint: 600,\n            settings: {\n                slidesToShow: 3,\n                slidesToScroll: 1\n            }\n        },\n        {\n            breakpoint: 480,\n            settings: {\n                slidesToShow: 3,\n                slidesToScroll: 1\n            }\n        }\n        // You can unslick at a given breakpoint now by adding:\n        // settings: \"unslick\"\n        // instead of a settings object\n    ]\n});\n\n            \n})(jQuery);\n    \n\n// SWIPER SLIDER //\ndocument.addEventListener(\"DOMContentLoaded\", function () {\n  var swiper3 = new Swiper(\".swiper-thumb2\", {\n    spaceBetween: 10,\n    slidesPerView: 6,\n    freeMode: true,\n    watchSlidesProgress: true,\n    autoplay: {\n      delay: 2500,\n      disableOnInteraction: false,\n    },\n  });\n  var swiper4 = new Swiper(\".swiper-testimonial-2\", {\n    spaceBetween: 10,\n    loop: true,\n    navigation: {\n      nextEl: \".swiper-button-next\",\n      prevEl: \".swiper-button-prev\",\n    },\n    autoplay: {\n      delay: 2500,\n      disableOnInteraction: false,\n    },\n    thumbs: {\n      swiper: swiper3,\n    },\n  });\n});\n\n\n\n\ndocument.addEventListener(\"DOMContentLoaded\", () => {\n  // Safe checks for elements to avoid errors\n  const categories = document.querySelectorAll(\".category\");\n\n  // Check if categories exist before adding event listeners\n  if (categories.length > 0) {\n    categories.forEach((category) => {\n      category.addEventListener(\"click\", () => {\n        categories.forEach((cat) => cat.classList.remove(\"active\"));\n        category.classList.add(\"active\");\n      });\n    });\n  }\n\n});\n\n\n\nconst slider = document.getElementById('balance-slider');\nconst selectedValue = document.getElementById('selectedValue');\n\nif (slider && selectedValue) {\n  // Function to update the background gradient\n  function updateSliderBackground() {\n    const value = slider.value;\n    const max = slider.max;\n    const percentage = (value / max) * 100;\n    slider.style.background = `linear-gradient(to right, Navy ${percentage}%, #e0e0e0 ${percentage}%)`;\n  }\n\n  // Event listener for slider input\n  slider.addEventListener('input', function () {\n    selectedValue.textContent = this.value;\n    updateSliderBackground();\n  });\n\n  // Initialize the slider background on page load\n  updateSliderBackground();\n}\n"], "names": ["$", "document", "ready", "_$$slick", "_$$slick2", "_$$slick3", "_$$slick4", "_$$slick5", "_$$slick6", "_$$slick7", "_$$slick8", "_rev$slick", "on", "addClass", "removeClass", "windowOn", "window", "scroll", "scrollTop", "click", "event", "toggleClass", "change", "is", "hide", "show", "vlMenuWrap", "clone", "vlSideMenu", "append", "find", "length", "parent", "sideMenuList", "e", "console", "log", "preventDefault", "hasClass", "siblings", "slideDown", "slideUp", "animateElements", "each", "elementPos", "offset", "top", "topOfWindow", "percent", "attr", "percentage", "parseInt", "animate", "data", "height", "circleProgress", "startAngle", "Math", "PI", "value", "size", "thickness", "emptyFill", "fill", "color", "progress", "<PERSON><PERSON><PERSON><PERSON>", "text", "toFixed", "stop", "AOS", "init", "duration", "easing", "anchorPlacement", "disable", "once", "magnificPopup", "type", "progressPath", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "getTotalLength", "style", "transition", "WebkitTransition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "getBoundingClientRect", "updateProgress", "j<PERSON><PERSON><PERSON>", "colors", "colors2", "select1", "tes1", "slick", "slidesToShow", "slidesToScroll", "arrows", "autoplay", "autoplaySpeed", "loop", "focusOnSelect", "infinite", "prevArrow", "nextArrow", "dots", "vertical", "margin", "centerMode", "_defineProperty", "breakpoint", "settings", "centerPadding", "owlCarousel", "responsiveClass", "nav", "responsive", "items", "rev", "currentSlide", "cur", "$slides", "next", "prev", "$prev", "$next", "nextSlide", "speed", "slidesPerRow", "swipe", "customPaging", "slider", "i", "hover", "staggerAmount", "translateXValue", "delayValue", "animatedTextElements", "querySelectorAll", "for<PERSON>ach", "element", "animationSplitText", "SplitText", "gsap", "from", "words", "delay", "x", "autoAlpha", "stagger", "scrollTrigger", "trigger", "start", "easeType", "chars", "ease", "animation", "kill", "split", "revert", "linesClass", "set", "perspective", "opacity", "to", "y", "rotateX", "Back", "easeOut", "parentOffset", "relX", "pageX", "left", "relY", "pageY", "css", "niceSelect", "addEventListener", "swiper3", "Swiper", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "freeMode", "watchSlidesProgress", "disableOnInteraction", "swiper4", "navigation", "nextEl", "prevEl", "thumbs", "swiper", "categories", "category", "cat", "classList", "remove", "add", "getElementById", "selected<PERSON><PERSON><PERSON>", "updateSliderBackground", "max", "background", "concat", "textContent"], "sourceRoot": ""}