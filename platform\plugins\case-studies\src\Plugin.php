<?php

namespace Shaqi\CaseStudies;

use <PERSON><PERSON><PERSON>\PluginManagement\Abstracts\PluginOperationAbstract;
use <PERSON><PERSON>qi\Widget\Models\Widget as DashboardWidget;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('case_study_category_pivot');
        Schema::dropIfExists('case_studies');
        Schema::dropIfExists('case_study_categories');
        Schema::dropIfExists('case_studies_translations');
        Schema::dropIfExists('case_study_categories_translations');

        DashboardWidget::query()
            ->where('widget_id', 'widget_case_studies_recent')
            ->each(fn (DashboardWidget $dashboardWidget) => $dashboardWidget->delete());
    }
}
