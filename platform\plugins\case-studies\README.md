# Case Studies Plugin

A comprehensive case studies management plugin for the Shaqi CMS platform.

## Features

- **Case Studies Management**: Create, edit, and manage case studies with rich content
- **Category System**: Organize case studies with hierarchical categories
- **SEO Friendly**: Built-in SEO optimization with meta tags and structured URLs
- **Responsive Design**: Mobile-friendly templates and layouts
- **Admin Interface**: Intuitive admin panel for content management
- **API Support**: RESTful API endpoints for external integrations
- **Multilingual Support**: Translation-ready with language files
- **Media Management**: Support for images and galleries
- **Status Management**: Draft, published, and featured status options

## Installation

1. Place the plugin in `platform/plugins/case-studies/`
2. Run migrations: `php artisan migrate`
3. Activate the plugin in Admin Panel > Plugins

## Database Schema

### Case Studies Table
- `id` - Primary key
- `name` - Case study title
- `description` - Short description
- `content` - Full content (rich text)
- `image` - Featured image
- `client_name` - Client name
- `client_logo` - Client logo
- `project_url` - Project URL
- `challenge` - Project challenge description
- `solution` - Solution description
- `results` - Results achieved
- `technologies` - Technologies used (JSON)
- `gallery` - Image gallery (JSON)
- `is_featured` - Featured flag
- `status` - Publication status
- `author_id` - Author reference
- `author_type` - Author model type
- `views` - View counter
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp

### Case Study Categories Table
- `id` - Primary key
- `name` - Category name
- `parent_id` - Parent category reference
- `description` - Category description
- `status` - Publication status
- `author_id` - Author reference
- `author_type` - Author model type
- `icon` - Category icon
- `order` - Display order
- `is_featured` - Featured flag
- `is_default` - Default category flag
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp

## Permissions
- `case-studies.index` - View case studies list
- `case-studies.create` - Create new case studies
- `case-studies.edit` - Edit case studies
- `case-studies.destroy` - Delete case studies
- `case-study-categories.*` - Category management permissions

## Theme Integration
The plugin provides theme templates that can be customized:
- `plugins/case-studies::themes.case-study` - Single case study page
- `plugins/case-studies::themes.category` - Category page

## API Endpoints
- `GET /api/v1/case-studies` - List case studies
- `GET /api/v1/case-studies/{slug}` - Get case study by slug
- `GET /api/v1/case-study-categories` - List categories
- `GET /api/v1/case-study-categories/{slug}` - Get category by slug

## Configuration

The plugin includes configuration files:
- `config/permissions.php` - Permission definitions
- `config/general.php` - General settings

## Helper Functions

The plugin provides helper functions for theme development:
- `get_featured_case_studies()` - Get featured case studies
- `get_recent_case_studies()` - Get recent case studies
- `get_case_studies_by_category()` - Get case studies by category
- `get_all_case_study_categories()` - Get all categories

## Customization

### Templates
Override templates by copying them to your theme:
```
themes/your-theme/views/case-study.blade.php
themes/your-theme/views/case-study-category.blade.php
```

### Styling
Add custom CSS in your theme or override the plugin's CSS files.

## Support

For support and documentation, visit [https://shaqi.com](https://shaqi.com)
