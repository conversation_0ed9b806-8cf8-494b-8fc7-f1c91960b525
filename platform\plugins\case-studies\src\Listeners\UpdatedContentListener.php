<?php

namespace Shaqi\CaseStudies\Listeners;

use Shaqi\Base\Events\UpdatedContentEvent;
use Shaqi\CaseStudies\Models\CaseStudy;
use Shaqi\CaseStudies\Services\StoreCaseStudyCategoryService;
use Exception;

class UpdatedContentListener
{
    public function handle(UpdatedContentEvent $event): void
    {
        try {
            if ($event->screen === CASE_STUDY_MODULE_SCREEN_NAME && $event->data instanceof CaseStudy) {
                if ($event->request->has('categories')) {
                    $event->data->categories()->sync($event->request->input('categories', []));
                }
            }

            if ($event->screen === CASE_STUDY_CATEGORY_MODULE_SCREEN_NAME) {
                app(StoreCaseStudyCategoryService::class)->execute($event->request, $event->data);
            }
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}
