<?php

namespace Shaqi\CaseStudies\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Shaqi\Media\Facades\RvMedia;

class ListCaseStudyResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'image' => $this->image ? RvMedia::getImageUrl($this->image) : null,
            'client_name' => $this->client_name,
            'client_logo' => $this->client_logo ? RvMedia::getImageUrl($this->client_logo) : null,
            'project_url' => $this->project_url,
            'is_featured' => $this->is_featured,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'url' => $this->url,
            'categories' => CategoryResource::collection($this->whenLoaded('categories')),
            'author' => $this->whenLoaded('author', function () {
                return [
                    'id' => $this->author->id,
                    'name' => $this->author->name,
                ];
            }),
        ];
    }
}
