{"__meta": {"id": "X24664c835aba3c2ae46563188fc51b9f", "datetime": "2025-07-18 22:51:49", "utime": **********.27895, "method": "POST", "uri": "/ajax/slug/create", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752879108.254854, "end": **********.278974, "duration": 1.0241200923919678, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1752879108.254854, "relative_start": 0, "end": **********.170134, "relative_end": **********.170134, "duration": 0.9152801036834717, "duration_str": "915ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.170152, "relative_start": 0.9152979850769043, "end": **********.278977, "relative_end": 2.86102294921875e-06, "duration": 0.1088249683380127, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 41814624, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST ajax/slug/create", "middleware": "web, core", "as": "slug.create", "controller": "Sha<PERSON>\\Slug\\Http\\Controllers\\SlugController@store", "namespace": "<PERSON><PERSON><PERSON>\\Slug\\Http\\Controllers", "prefix": "/ajax/slug", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fslug%2Fsrc%2FHttp%2FControllers%2FSlugController.php&line=18\" onclick=\"\">vendor/shaqi/slug/src/Http/Controllers/SlugController.php:18-25</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01878, "accumulated_duration_str": "18.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists(select * from `slugs` where (`key` = 'long-term-rental-2-room-apartment-with-air-conditioning-and-parking-krakow' and `prefix` = 'case-study') and `id` != '0') as `exists`", "type": "query", "params": [], "bindings": ["long-term-rental-2-room-apartment-with-air-conditioning-and-parking-krakow", "case-study", "0"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/shaqi/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\Services\\SlugService.php", "line": 42}, {"index": 12, "namespace": null, "name": "vendor/shaqi/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\Services\\SlugService.php", "line": 23}, {"index": 13, "namespace": null, "name": "vendor/shaqi/slug/src/Http/Controllers/SlugController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\Http\\Controllers\\SlugController.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.250364, "duration": 0.01878, "duration_str": "18.78ms", "memory": 0, "memory_str": null, "filename": "SlugService.php:42", "source": {"index": 11, "namespace": null, "name": "vendor/shaqi/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\Services\\SlugService.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fslug%2Fsrc%2FServices%2FSlugService.php&line=42", "ajax": false, "filename": "SlugService.php", "line": "42"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn/case-studies/case-studies/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "url": "[]"}, "request": {"path_info": "/ajax/slug/create", "status_code": "<pre class=sf-dump id=sf-dump-2092687753 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2092687753\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-320397570 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-320397570\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1109031927 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Long-term rental 2-room apartment with air conditioning and parking, Krak&#243;w</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Shaqi\\CaseStudies\\Models\\CaseStudy</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109031927\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-512158712 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">202</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZJMndFL0FnL0g1ekdtdFZUaTc4ZGc9PSIsInZhbHVlIjoiUCtsMm5hVmkyYjZTNTZPcmdVa3N6UUVPNGhIRmJ5Q2FGZ3pOcDNpNWNRNndtbUsxWTFyR3R3dW5mSFAyYzkyOWtzMmtteTJSRjVQOUkrWC9Bakx3bVZlYmxsM2U0NEhzRW1SaTVnbFc3ZjljbGR0QnpkNmxGNHhpdWljeERxbzEiLCJtYWMiOiJlZjFmMTNkMWEyMzZmNTYzYmM4NWI2N2Y2YzE3NzhmNjFmZTZkZjcyNTFlNmE4MzM1MjQ2MTEzOWM4M2FkZjI3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; _ga_FSKD72EXSB=GS2.1.s1752869141$o43$g1$t1752874702$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJqeUJpUklyZGNINDVwa09NNnJiTGc9PSIsInZhbHVlIjoiZ1picmMxRWU5V2xQb1haQVdaSEtsSVo1dGtFbnAzK3E4T0N6VVU1TThlck9Pb01hODBlQzVCeld0N2UrS29uMFlpSGdqZyttdzQ0K20rcE55WHhCbVZhb05BaXBSVHNEY1ZMa05kYThpbS9PUURhUWZuVkpsT1JzZW1DZ2ViR1NxWVg5aVZDRktqTzF6NkpqSHVKRmtwNWlhdHlNQVp3aWJmWU8rZnBCVURubVVsMjcxTjFvV2VZd01ibTlZc2xKcWRVMHJWNHdwNG1iaThsR1JLWTlEMDRkRGJUQjVlTlZPanpsL1FPMHlMUT0iLCJtYWMiOiIzZTBiNWRhYjZiNmFjOWZlZTVhNDVlNDdkNjRlZGI2NTA0MTZhYjdhZmFjOWJmYTBjYjMwNzA1YmQyZGE3YmUxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkZJMndFL0FnL0g1ekdtdFZUaTc4ZGc9PSIsInZhbHVlIjoiUCtsMm5hVmkyYjZTNTZPcmdVa3N6UUVPNGhIRmJ5Q2FGZ3pOcDNpNWNRNndtbUsxWTFyR3R3dW5mSFAyYzkyOWtzMmtteTJSRjVQOUkrWC9Bakx3bVZlYmxsM2U0NEhzRW1SaTVnbFc3ZjljbGR0QnpkNmxGNHhpdWljeERxbzEiLCJtYWMiOiJlZjFmMTNkMWEyMzZmNTYzYmM4NWI2N2Y2YzE3NzhmNjFmZTZkZjcyNTFlNmE4MzM1MjQ2MTEzOWM4M2FkZjI3IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6ImxxUTEyZjZoRnlEd3QxbVpjOGhhS2c9PSIsInZhbHVlIjoidW51QmpVT2VtWUdPNGRkQUFoYXN3YThhM1JhY3dOT1Q2UDhhckpLYTkrbVB2UXVyWS9uTkVuNUtjWk5vSDQ0VHBEN3JuYk9oYkRuWCtxMVpWY2dDdE1MUlpwUkxSNzRBMTVpanJPZGxIV0UzSmwxTHpKSkNoTHNZcVJqOEU3azIiLCJtYWMiOiI1YmNkZWZmODE3OGRmMzk4YzNkZDI2MTlkNTNiZjEwMDM5NGMyYjU3ZDk0NTk3NjFhYjVmZjg0NjI3YzdiMDc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512158712\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-674784641 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|93AZBoOuq93wZdZjYcXin3OIxaW8YfC1pd64jsSLusTXT7oOXJKvnP4Pzefq|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pfgazGmSZFAgAHgBDhcfMpRcFY48ppQYxnrLba4f</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674784641\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-236644567 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 22:51:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6Imx1THBrbElzWlpIOC9neWRTVG1rdVE9PSIsInZhbHVlIjoiV0VLUTlWZjYvUGl0SjNVZElYZE0vU0RKZ3VPUXpwWXBSMksxNkdmVy9PM3YzT1dzbjM3bWZ4aTdYUEs5M0I4bXlhMlpodWZBTFlJL2p6OXBuMzI0ZGgxRlA0ZHFaTTRQNWJRQms1ZDVqUzFjczJhRVFUQjVFSHBNM21vQ1BQa3YiLCJtYWMiOiJlZjEyOTE5ZTA2YmFjODRmMjFmZWZiMGY2MTgyZjBlNWZkMzhjOTJlMDQzYzI1YjZiZjYzMDViODNjNGQ3ZmNmIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:51:49 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6InJuTW5MbmdIMzBabWlSbGQzRjQxc3c9PSIsInZhbHVlIjoiODdsT0N3cG9leTEwK0xQM2Fvb0ZjOVBGVFR1cm5vTDh5SGRjUXVTK2Y2bEd3czI1S3kvQWJjUUlHbk1VNDRuWkNYRHdlSklEWHpFV3NkZlc2d0xmQXJYYXJRQ0tQQUU1ODlER3o4SktTUlR5L05kSDF0ZVpIN3lvbzlsZ1FHOE0iLCJtYWMiOiI0MzFlMDI5N2VjNzdmYjZkNDc4Mzg3YTYyZGIzODJlOWExNjc3OGRkOTU2MmMxOTQwMDE5NmZkY2Y3YTJmYjA5IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:51:49 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6Imx1THBrbElzWlpIOC9neWRTVG1rdVE9PSIsInZhbHVlIjoiV0VLUTlWZjYvUGl0SjNVZElYZE0vU0RKZ3VPUXpwWXBSMksxNkdmVy9PM3YzT1dzbjM3bWZ4aTdYUEs5M0I4bXlhMlpodWZBTFlJL2p6OXBuMzI0ZGgxRlA0ZHFaTTRQNWJRQms1ZDVqUzFjczJhRVFUQjVFSHBNM21vQ1BQa3YiLCJtYWMiOiJlZjEyOTE5ZTA2YmFjODRmMjFmZWZiMGY2MTgyZjBlNWZkMzhjOTJlMDQzYzI1YjZiZjYzMDViODNjNGQ3ZmNmIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:51:49 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6InJuTW5MbmdIMzBabWlSbGQzRjQxc3c9PSIsInZhbHVlIjoiODdsT0N3cG9leTEwK0xQM2Fvb0ZjOVBGVFR1cm5vTDh5SGRjUXVTK2Y2bEd3czI1S3kvQWJjUUlHbk1VNDRuWkNYRHdlSklEWHpFV3NkZlc2d0xmQXJYYXJRQ0tQQUU1ODlER3o4SktTUlR5L05kSDF0ZVpIN3lvbzlsZ1FHOE0iLCJtYWMiOiI0MzFlMDI5N2VjNzdmYjZkNDc4Mzg3YTYyZGIzODJlOWExNjc3OGRkOTU2MmMxOTQwMDE5NmZkY2Y3YTJmYjA5IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:51:49 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236644567\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-314950467 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>url</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314950467\", {\"maxDepth\":0})</script>\n"}}