<?php

namespace Shaqi\CaseStudies\Http\Controllers\API;

use <PERSON>haqi\Api\Http\Controllers\BaseController;
use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\CaseStudies\Http\Resources\ListCaseStudyResource;
use <PERSON><PERSON>qi\CaseStudies\Http\Resources\CaseStudyResource;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyInterface;
use Illuminate\Http\Request;

class CaseStudyController extends BaseController
{
    public function __construct(protected CaseStudyInterface $caseStudyRepository)
    {
    }

    /**
     * List case studies
     *
     * @group Case Studies
     */
    public function index(Request $request)
    {
        $data = $this->caseStudyRepository
            ->advancedGet([
                'with' => ['categories', 'author', 'slugable'],
                'condition' => ['status' => BaseStatusEnum::PUBLISHED],
                'paginate' => [
                    'per_page' => $request->integer('per_page', 10),
                    'current_paged' => $request->integer('page', 1),
                ],
            ]);

        return $this
            ->httpResponse()
            ->setData(ListCaseStudyResource::collection($data))
            ->toApiResponse();
    }

    /**
     * Get case study by slug
     *
     * @group Case Studies
     */
    public function findBySlug(string $slug, Request $request)
    {
        $caseStudy = get_case_studies_by_slug($slug, true, ['categories', 'author']);

        if (! $caseStudy) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage('Not found');
        }

        return $this
            ->httpResponse()
            ->setData(new CaseStudyResource($caseStudy))
            ->toApiResponse();
    }

    /**
     * Get case study filters
     *
     * @group Case Studies
     */
    public function getFilters(Request $request)
    {
        $filters = [];
        
        if ($request->has('category_id')) {
            $filters['category_id'] = $request->input('category_id');
        }
        
        if ($request->has('keyword')) {
            $filters['keyword'] = $request->input('keyword');
        }

        $filters['per_page'] = $request->integer('per_page', 10);

        $data = $this->caseStudyRepository->getFilters($filters);

        return $this
            ->httpResponse()
            ->setData(ListCaseStudyResource::collection($data))
            ->toApiResponse();
    }

    /**
     * Search case studies
     *
     * @group Case Studies
     */
    public function getSearch(Request $request)
    {
        $keyword = $request->input('q');
        $limit = $request->integer('limit', 10);

        $data = $this->caseStudyRepository->getSearch($keyword, $limit);

        return $this
            ->httpResponse()
            ->setData(ListCaseStudyResource::collection($data))
            ->toApiResponse();
    }
}
