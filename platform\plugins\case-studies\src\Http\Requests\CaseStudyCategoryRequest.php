<?php

namespace Shaqi\CaseStudies\Http\Requests;

use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CaseStudyCategoryRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:120',
            'parent_id' => 'nullable|integer|min:0',
            'description' => 'nullable|string|max:400',
            'order' => 'nullable|integer|min:0',
            'is_featured' => 'nullable|boolean',
            'is_default' => 'nullable|boolean',
            'status' => Rule::in(BaseStatusEnum::values()),
            'icon' => 'nullable|string|max:60',
        ];
    }
}
