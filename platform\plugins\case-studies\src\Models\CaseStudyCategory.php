<?php

namespace <PERSON><PERSON>qi\CaseStudies\Models;

use <PERSON>haqi\Base\Casts\SafeContent;
use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use Shaqi\Base\Models\BaseModel;
use Shaqi\Base\Contracts\HasTreeCategory as HasTreeCategoryContract;
use Shaqi\Base\Traits\HasTreeCategory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class CaseStudyCategory extends BaseModel implements HasTreeCategoryContract
{
    use HasTreeCategory;

    protected $table = 'case_study_categories';

    protected $fillable = [
        'name',
        'description',
        'parent_id',
        'icon',
        'is_featured',
        'order',
        'is_default',
        'status',
        'author_id',
        'author_type',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'is_default' => 'bool',
        'is_featured' => 'bool',
        'order' => 'int',
    ];

    protected static function booted(): void
    {
        static::deleted(function (CaseStudyCategory $category): void {
            $category->children()->each(fn (Model $child) => $child->delete());

            $category->caseStudies()->detach();
        });
    }

    public function caseStudies(): BelongsToMany
    {
        return $this->belongsToMany(CaseStudy::class, 'case_study_category_pivot', 'category_id', 'case_study_id');
    }
}
