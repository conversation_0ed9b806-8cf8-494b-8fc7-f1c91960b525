[2025-07-18 22:32:13] local.ERROR: Undefined constant "Shaqi\CaseStudies\Providers\BASE_FILTER_SITE_MAP_FOR_ADMIN_LIST" {"exception":"[object] (Error(code: 0): Undefined constant \"Shaqi\\CaseStudies\\Providers\\BASE_FILTER_SITE_MAP_FOR_ADMIN_LIST\" at D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\HookServiceProvider.php:18)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\CaseStudies\\Providers\\HookServiceProvider->boot()
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\CaseStudies\\Providers\\HookServiceProvider))
#7 D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\CaseStudiesServiceProvider.php(183): Illuminate\\Foundation\\Application->register(Object(Shaqi\\CaseStudies\\Providers\\HookServiceProvider))
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1152): Shaqi\\CaseStudies\\Providers\\CaseStudiesServiceProvider->Shaqi\\CaseStudies\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#9 D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\CaseStudiesServiceProvider.php(177): Illuminate\\Foundation\\Application->booted(Object(Closure))
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\CaseStudies\\Providers\\CaseStudiesServiceProvider->boot()
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#16 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\CaseStudies\\Providers\\CaseStudiesServiceProvider))
#17 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Services\\PluginService.php(98): Illuminate\\Foundation\\Application->register(Object(Shaqi\\CaseStudies\\Providers\\CaseStudiesServiceProvider))
#18 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Shaqi\\PluginManagement\\Services\\PluginService->activate('case-studies')
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Shaqi\\PluginManagement\\Services\\PluginService))
#20 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#25 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Shaqi\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
[2025-07-18 22:33:10] local.ERROR: rename(D:\laragon\www\goalconversion\bootstrap\cache\pac15D9.tmp,D:\laragon\www\goalconversion\bootstrap\cache/packages.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(D:\\laragon\\www\\goalconversion\\bootstrap\\cache\\pac15D9.tmp,D:\\laragon\\www\\goalconversion\\bootstrap\\cache/packages.php): Access is denied (code: 5) at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(D:\\\\larag...', 'D:\\\\laragon\\\\www\\\\...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(D:\\\\larag...', 'D:\\\\laragon\\\\www\\\\...', 233)
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('D:\\\\laragon\\\\www\\\\...', 'D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(182): Illuminate\\Filesystem\\Filesystem->replace('D:\\\\laragon\\\\www\\\\...', '<?php return ar...')
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(91): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(80): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-07-18 22:38:53] local.ERROR: Trait "Shaqi\Base\Supports\TreeCategory\TreeCategoryTrait" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Shaqi\\Base\\Supports\\TreeCategory\\TreeCategoryTrait\" not found at D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Models\\CaseStudyCategory.php:13)
[stacktrace]
#0 {main}
"} 
[2025-07-18 22:44:10] local.ERROR: Trait "Shaqi\Base\Supports\TreeCategory\TreeCategoryTrait" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Shaqi\\Base\\Supports\\TreeCategory\\TreeCategoryTrait\" not found at D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Models\\CaseStudyCategory.php:13)
[stacktrace]
#0 {main}
"} 
[2025-07-18 22:49:48] local.ERROR: Shaqi\Table\Abstracts\TableAbstract::__construct(): Argument #1 ($table) must be of type Yajra\DataTables\DataTables, Shaqi\CaseStudies\Repositories\Eloquent\CaseStudyRepository given, called in D:\laragon\www\goalconversion\platform\plugins\case-studies\src\Tables\CaseStudyTable.php on line 30 {"userId":1,"exception":"[object] (TypeError(code: 0): Shaqi\\Table\\Abstracts\\TableAbstract::__construct(): Argument #1 ($table) must be of type Yajra\\DataTables\\DataTables, Shaqi\\CaseStudies\\Repositories\\Eloquent\\CaseStudyRepository given, called in D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Tables\\CaseStudyTable.php on line 30 at D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\table\\src\\Abstracts\\TableAbstract.php:123)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Tables\\CaseStudyTable.php(30): Shaqi\\Table\\Abstracts\\TableAbstract->__construct(Object(Shaqi\\CaseStudies\\Repositories\\Eloquent\\CaseStudyRepository), Object(Illuminate\\Routing\\UrlGenerator))
#1 [internal function]: Shaqi\\CaseStudies\\Tables\\CaseStudyTable->__construct(Object(Shaqi\\CaseStudies\\Repositories\\Eloquent\\CaseStudyRepository), Object(Illuminate\\Routing\\UrlGenerator))
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(989): ReflectionClass->newInstanceArgs(Array)
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build('Shaqi\\\\CaseStudi...')
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1051): Illuminate\\Container\\Container->resolve('Shaqi\\\\CaseStudi...', Array, true)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('Shaqi\\\\CaseStudi...', Array)
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1033): Illuminate\\Container\\Container->make('Shaqi\\\\CaseStudi...', Array)
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('Shaqi\\\\CaseStudi...')
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(60): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(Shaqi\\CaseStudies\\Http\\Controllers\\CaseStudyController), 'index')
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(41): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(Shaqi\\CaseStudies\\Http\\Controllers\\CaseStudyController), 'index')
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\CaseStudies\\Http\\Controllers\\CaseStudyController), 'index')
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#14 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->Shaqi\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\goalconversion\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\goalconversion\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 {main}
"} 
