[2025-07-18 22:32:13] local.ERROR: Undefined constant "Shaqi\CaseStudies\Providers\BASE_FILTER_SITE_MAP_FOR_ADMIN_LIST" {"exception":"[object] (Error(code: 0): Undefined constant \"Shaqi\\CaseStudies\\Providers\\BASE_FILTER_SITE_MAP_FOR_ADMIN_LIST\" at D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\HookServiceProvider.php:18)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\CaseStudies\\Providers\\HookServiceProvider->boot()
#1 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\CaseStudies\\Providers\\HookServiceProvider))
#7 D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\CaseStudiesServiceProvider.php(183): Illuminate\\Foundation\\Application->register(Object(Shaqi\\CaseStudies\\Providers\\HookServiceProvider))
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1152): Shaqi\\CaseStudies\\Providers\\CaseStudiesServiceProvider->Shaqi\\CaseStudies\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#9 D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\CaseStudiesServiceProvider.php(177): Illuminate\\Foundation\\Application->booted(Object(Closure))
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\CaseStudies\\Providers\\CaseStudiesServiceProvider->boot()
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#16 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(898): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\CaseStudies\\Providers\\CaseStudiesServiceProvider))
#17 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Services\\PluginService.php(98): Illuminate\\Foundation\\Application->register(Object(Shaqi\\CaseStudies\\Providers\\CaseStudiesServiceProvider))
#18 D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\plugin-management\\src\\Commands\\PluginActivateCommand.php(28): Shaqi\\PluginManagement\\Services\\PluginService->activate('case-studies')
#19 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Commands\\PluginActivateCommand->handle(Object(Shaqi\\PluginManagement\\Services\\PluginService))
#20 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#25 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Shaqi\\PluginManagement\\Commands\\PluginActivateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\laragon\\www\\goalconversion\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\goalconversion\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
[2025-07-18 22:33:10] local.ERROR: rename(D:\laragon\www\goalconversion\bootstrap\cache\pac15D9.tmp,D:\laragon\www\goalconversion\bootstrap\cache/packages.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(D:\\laragon\\www\\goalconversion\\bootstrap\\cache\\pac15D9.tmp,D:\\laragon\\www\\goalconversion\\bootstrap\\cache/packages.php): Access is denied (code: 5) at D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(D:\\\\larag...', 'D:\\\\laragon\\\\www\\\\...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(D:\\\\larag...', 'D:\\\\laragon\\\\www\\\\...', 233)
#2 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('D:\\\\laragon\\\\www\\\\...', 'D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(182): Illuminate\\Filesystem\\Filesystem->replace('D:\\\\laragon\\\\www\\\\...', '<?php return ar...')
#4 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#5 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#6 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(91): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(80): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-07-18 22:38:53] local.ERROR: Trait "Shaqi\Base\Supports\TreeCategory\TreeCategoryTrait" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Shaqi\\Base\\Supports\\TreeCategory\\TreeCategoryTrait\" not found at D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Models\\CaseStudyCategory.php:13)
[stacktrace]
#0 {main}
"} 
[2025-07-18 22:44:10] local.ERROR: Trait "Shaqi\Base\Supports\TreeCategory\TreeCategoryTrait" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Shaqi\\Base\\Supports\\TreeCategory\\TreeCategoryTrait\" not found at D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Models\\CaseStudyCategory.php:13)
[stacktrace]
#0 {main}
"} 
