<?php

namespace <PERSON>haqi\CaseStudies\Services;

use Shaqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\Base\Facades\AdminHelper;
use <PERSON>haqi\Base\Supports\Helper;
use <PERSON><PERSON>qi\CaseStudies\Models\CaseStudy;
use <PERSON><PERSON>qi\CaseStudies\Models\CaseStudyCategory;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyInterface;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyCategoryInterface;
use Shaqi\Media\Facades\RvMedia;
use Shaqi\SeoHelper\Facades\SeoHelper;
use Shaqi\SeoHelper\SeoOpenGraph;
use <PERSON><PERSON>qi\Slug\Models\Slug;
use Shaqi\Theme\Facades\AdminBar;
use Shaqi\Theme\Facades\Theme;
use Shaqi\Theme\Facades\SiteMapManager;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;

class CaseStudyService
{
    public function handleFrontRoutes(Slug|array $slug): Slug|array|Builder
    {
        if (! $slug instanceof Slug) {
            return $slug;
        }

        $condition = [
            'id' => $slug->reference_id,
            'status' => BaseStatusEnum::PUBLISHED,
        ];

        if (AdminHelper::isPreviewing()) {
            Arr::forget($condition, 'status');
        }

        switch ($slug->reference_type) {
            case CaseStudy::class:
                $caseStudy = app(CaseStudyInterface::class)->getFirstBy($condition, ['*'], ['categories', 'author']);

                if (empty($caseStudy)) {
                    abort(404);
                }

                SeoHelper::setTitle($caseStudy->name)
                    ->setDescription($caseStudy->description);

                $meta = new SeoOpenGraph();
                if ($caseStudy->image) {
                    $meta->setImage(RvMedia::getImageUrl($caseStudy->image));
                }
                $meta->setDescription($caseStudy->description);
                $meta->setUrl($caseStudy->url);
                $meta->setTitle($caseStudy->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);

                if (function_exists('admin_bar')) {
                    AdminBar::setIsDisplay(true)
                        ->setLinksNofollow(false)
                        ->setMenuOptionData([
                            'slug' => $caseStudy->slug,
                            'original_value' => $caseStudy->name,
                            'edit_link' => route('case-studies.edit', $caseStudy->id),
                        ]);
                }

                Helper::handleViewCount($caseStudy, 'viewed_case_study');

                Theme::breadcrumb()
                    ->add(__('Home'), route('public.index'))
                    ->add(__('Case Studies'), route('public.case-studies'))
                    ->add($caseStudy->name, $caseStudy->url);

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, CASE_STUDY_MODULE_SCREEN_NAME, $caseStudy);

                return [
                    'view' => 'case-study',
                    'default_view' => 'plugins/case-studies::themes.case-study',
                    'data' => compact('caseStudy'),
                    'slug' => $caseStudy->slug,
                ];

            case CaseStudyCategory::class:
                $category = app(CaseStudyCategoryInterface::class)->getFirstBy($condition);

                if (empty($category)) {
                    abort(404);
                }

                SeoHelper::setTitle($category->name)
                    ->setDescription($category->description);

                $meta = new SeoOpenGraph();
                $meta->setDescription($category->description);
                $meta->setUrl($category->url);
                $meta->setTitle($category->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);

                if (function_exists('admin_bar')) {
                    AdminBar::setIsDisplay(true)
                        ->setLinksNofollow(false)
                        ->setMenuOptionData([
                            'slug' => $category->slug,
                            'original_value' => $category->name,
                            'edit_link' => route('case-study-categories.edit', $category->id),
                        ]);
                }

                $caseStudies = app(CaseStudyInterface::class)->getByCategory($category->id, 12);

                Theme::breadcrumb()
                    ->add(__('Home'), route('public.index'))
                    ->add(__('Case Studies'), route('public.case-studies'))
                    ->add($category->name, $category->url);

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, CASE_STUDY_CATEGORY_MODULE_SCREEN_NAME, $category);

                return [
                    'view' => 'case-study-category',
                    'default_view' => 'plugins/case-studies::themes.category',
                    'data' => compact('category', 'caseStudies'),
                    'slug' => $category->slug,
                ];
        }

        return $slug;
    }

    public function getSiteMap(): string
    {
        $caseStudies = app(CaseStudyInterface::class)->getDataSiteMap();

        $siteMap = '';
        foreach ($caseStudies as $caseStudy) {
            $siteMap .= SiteMapManager::getSiteMapRoute($caseStudy->url, $caseStudy->updated_at);
        }

        $categories = app(CaseStudyCategoryInterface::class)->getDataSiteMap();

        foreach ($categories as $category) {
            $siteMap .= SiteMapManager::getSiteMapRoute($category->url, $category->updated_at);
        }

        return $siteMap;
    }
}
