<?php

namespace <PERSON>haqi\CaseStudies\Http\Controllers;

use Shaqi\Base\Events\BeforeEditContentEvent;
use Shaqi\Base\Events\CreatedContentEvent;
use <PERSON>haqi\Base\Events\DeletedContentEvent;
use <PERSON>haqi\Base\Events\UpdatedContentEvent;
use <PERSON>haqi\Base\Facades\PageTitle;
use Shaqi\Base\Forms\FormBuilder;
use Shaqi\Base\Http\Controllers\BaseController;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\CaseStudies\Forms\CaseStudyForm;
use Shaqi\CaseStudies\Http\Requests\CaseStudyRequest;
use Shaqi\CaseStudies\Models\CaseStudy;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyInterface;
use Shaqi\CaseStudies\Tables\CaseStudyTable;
use Exception;
use Illuminate\Http\Request;

class CaseStudyController extends BaseController
{
    public function __construct(protected CaseStudyInterface $caseStudyRepository)
    {
    }

    public function index(CaseStudyTable $table)
    {
        PageTitle::setTitle(trans('plugins/case-studies::case-studies.menu'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/case-studies::case-studies.create'));

        return $formBuilder->create(CaseStudyForm::class)->renderForm();
    }

    public function store(CaseStudyRequest $request, BaseHttpResponse $response)
    {
        $caseStudy = $this->caseStudyRepository->createOrUpdate($request->input());

        event(new CreatedContentEvent(CASE_STUDY_MODULE_SCREEN_NAME, $request, $caseStudy));

        return $response
            ->setPreviousUrl(route('case-studies.index'))
            ->setNextUrl(route('case-studies.edit', $caseStudy->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function show(int|string $id, Request $request, BaseHttpResponse $response)
    {
        $caseStudy = $this->caseStudyRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $caseStudy));

        return view('plugins/case-studies::case-studies.show', compact('caseStudy'));
    }

    public function edit(int|string $id, FormBuilder $formBuilder, Request $request)
    {
        $caseStudy = $this->caseStudyRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $caseStudy));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $caseStudy->name]));

        return $formBuilder->create(CaseStudyForm::class, ['model' => $caseStudy])->renderForm();
    }

    public function update(int|string $id, CaseStudyRequest $request, BaseHttpResponse $response)
    {
        $caseStudy = $this->caseStudyRepository->findOrFail($id);

        $caseStudy->fill($request->input());

        $this->caseStudyRepository->createOrUpdate($caseStudy);

        // event(new UpdatedContentEvent(CASE_STUDY_MODULE_SCREEN_NAME, $request, $caseStudy));

        return $response
            ->setPreviousUrl(route('case-studies.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(int|string $id, Request $request, BaseHttpResponse $response)
    {
        try {
            $caseStudy = $this->caseStudyRepository->findOrFail($id);

            $this->caseStudyRepository->delete($caseStudy);

            event(new DeletedContentEvent(CASE_STUDY_MODULE_SCREEN_NAME, $request, $caseStudy));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function getWidgetRecentCaseStudies(Request $request, BaseHttpResponse $response)
    {
        $limit = $request->integer('paginate', 5);
        $categoryId = $request->input('category_id');

        $caseStudies = $this->caseStudyRepository->getRecentCaseStudies($limit, $categoryId);

        return $response->setData(view('plugins/case-studies::case-studies.widgets.recent-case-studies', compact('caseStudies', 'limit', 'categoryId'))->render());
    }
}
