<?php

namespace Shaqi\CaseStudies\Repositories\Interfaces;

use <PERSON>haqi\Support\Repositories\Interfaces\RepositoryInterface;
use Illuminate\Support\Collection;

interface CaseStudyCategoryInterface extends RepositoryInterface
{
    public function getDataSiteMap(): Collection;

    public function getFeaturedCategories(int $limit = 5, array $with = ['slugable']): Collection;

    public function getAllCategories(array $condition = [], array $with = []): Collection;

    public function getAllCategoriesWithChildren(array $condition = [], array $with = [], array $select = ['*']): Collection;

    public function getPopularCategories(int $limit = 10, array $with = ['slugable'], array $withCount = ['caseStudies']): Collection;

    public function getCategories(array $select, array $orderBy, array $condition = []): Collection;
}
