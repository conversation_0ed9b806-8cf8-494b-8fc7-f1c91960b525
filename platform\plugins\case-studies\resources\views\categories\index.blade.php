@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="widget meta-boxes">
                <div class="widget-title">
                    <h4>{{ trans('plugins/case-studies::categories.menu') }}</h4>
                </div>
                <div class="widget-body">
                    <div class="tree-categories">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="dd" id="nestable">
                                    {!! BaseHelper::renderTreeCategories($categories, [
                                        'canEdit' => true,
                                        'canDelete' => true,
                                        'createRoute' => 'case-study-categories.create',
                                        'editRoute' => 'case-study-categories.edit',
                                        'deleteRoute' => 'case-study-categories.destroy',
                                    ]) !!}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="widget meta-boxes">
                                    <div class="widget-title">
                                        <h4>{{ trans('plugins/case-studies::categories.create') }}</h4>
                                    </div>
                                    <div class="widget-body">
                                        <a href="{{ route('case-study-categories.create') }}" class="btn btn-primary">
                                            {{ trans('plugins/case-studies::categories.create') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
