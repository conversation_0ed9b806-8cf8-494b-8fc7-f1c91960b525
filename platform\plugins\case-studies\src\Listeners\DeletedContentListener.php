<?php

namespace <PERSON>haqi\CaseStudies\Listeners;

use Shaqi\Base\Events\DeletedContentEvent;
use Exception;

class DeletedContentListener
{
    public function handle(DeletedContentEvent $event): void
    {
        try {
            // Handle any cleanup needed when content is deleted
            // For now, the model's booted() method handles relationship cleanup
        } catch (Exception $exception) {
            info($exception->getMessage());
        }
    }
}
