{"__meta": {"id": "Xffc6b6983f9232e984d99d154a03b9fb", "datetime": "2025-07-18 22:52:27", "utime": **********.080838, "method": "GET", "uri": "/gc/lgn/menu-items-count", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752879146.089326, "end": **********.080875, "duration": 0.991549015045166, "duration_str": "992ms", "measures": [{"label": "Booting", "start": 1752879146.089326, "relative_start": 0, "end": 1752879146.99804, "relative_end": 1752879146.99804, "duration": 0.9087140560150146, "duration_str": "909ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752879146.998084, "relative_start": 0.9087581634521484, "end": **********.080879, "relative_end": 4.0531158447265625e-06, "duration": 0.0827949047088623, "duration_str": "82.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 41555840, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET gc/lgn/menu-items-count", "middleware": "web, core, auth", "permission": false, "as": "menu-items-count", "controller": "Shaqi\\Base\\Http\\Controllers\\SystemController@getMenuItemsCount", "namespace": "Shaqi\\Base\\Http\\Controllers", "prefix": "/gc/lgn", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FHttp%2FControllers%2FSystemController.php&line=38\" onclick=\"\">vendor/shaqi/platform/base/src/Http/Controllers/SystemController.php:38-45</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00092, "accumulated_duration_str": "920μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.06006, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "url": "[]"}, "request": {"path_info": "/gc/lgn/menu-items-count", "status_code": "<pre class=sf-dump id=sf-dump-1550008424 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1550008424\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-893941220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-893941220\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2100112493 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2100112493\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-497033324 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InFLdW04eENrVGwvR1VZWlZ0ZmJIWWc9PSIsInZhbHVlIjoiZVlISFQwM0srVFFpS3JIR3Q1dkdKSmZ2N243OElsc1ZldzRhNlBQRW4wNHQyOEtJeC95V0tpK1E0S3JkMFpadW8yajZITlk4aFBrbnRRa25VazNXdXR5VHNxRElSdTk2alM2dkRKMmxrbzdtWnNpb21DUUE3VFdPV1lvVHZ6dzciLCJtYWMiOiJkZWJmZTJiNzdiZDY2OWI1NjE4MzZiNGUxNGQ2NzNmZDYwOTA1YzQ1MzE0ZmFiMWM4N2FlNmNiNTNkYTA2YWZhIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; _ga_FSKD72EXSB=GS2.1.s1752869141$o43$g1$t1752874702$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJqeUJpUklyZGNINDVwa09NNnJiTGc9PSIsInZhbHVlIjoiZ1picmMxRWU5V2xQb1haQVdaSEtsSVo1dGtFbnAzK3E4T0N6VVU1TThlck9Pb01hODBlQzVCeld0N2UrS29uMFlpSGdqZyttdzQ0K20rcE55WHhCbVZhb05BaXBSVHNEY1ZMa05kYThpbS9PUURhUWZuVkpsT1JzZW1DZ2ViR1NxWVg5aVZDRktqTzF6NkpqSHVKRmtwNWlhdHlNQVp3aWJmWU8rZnBCVURubVVsMjcxTjFvV2VZd01ibTlZc2xKcWRVMHJWNHdwNG1iaThsR1JLWTlEMDRkRGJUQjVlTlZPanpsL1FPMHlMUT0iLCJtYWMiOiIzZTBiNWRhYjZiNmFjOWZlZTVhNDVlNDdkNjRlZGI2NTA0MTZhYjdhZmFjOWJmYTBjYjMwNzA1YmQyZGE3YmUxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFLdW04eENrVGwvR1VZWlZ0ZmJIWWc9PSIsInZhbHVlIjoiZVlISFQwM0srVFFpS3JIR3Q1dkdKSmZ2N243OElsc1ZldzRhNlBQRW4wNHQyOEtJeC95V0tpK1E0S3JkMFpadW8yajZITlk4aFBrbnRRa25VazNXdXR5VHNxRElSdTk2alM2dkRKMmxrbzdtWnNpb21DUUE3VFdPV1lvVHZ6dzciLCJtYWMiOiJkZWJmZTJiNzdiZDY2OWI1NjE4MzZiNGUxNGQ2NzNmZDYwOTA1YzQ1MzE0ZmFiMWM4N2FlNmNiNTNkYTA2YWZhIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6InZiemo2dnFsKzJXQm16TCs3SEtlM1E9PSIsInZhbHVlIjoiZVBydTZzeWk5R3BFZ3RyVW1abkxQNmdzNTZmWUZnS1ArSzJCMU9QdnpwanRCdURETjZSYUc0ZTRoTXpmVzFmRjgyRUFjY3JGdnhFQ1FaVmxrK3VTTnRvODB6Q251eVQ1YUZjS1YxNmxtdVY3Z1dKTWF6MFVvc3EzSmRxWGFqcXgiLCJtYWMiOiI1YjRiOWE3ZDY0OWI0YmMzZWFjYjY0NDY1NjY2NjNhMDk1NmVlZThmOWRjMzU5YTZkZmQ5OTFkZTE1MTZlZjcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497033324\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-985670511 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|93AZBoOuq93wZdZjYcXin3OIxaW8YfC1pd64jsSLusTXT7oOXJKvnP4Pzefq|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pfgazGmSZFAgAHgBDhcfMpRcFY48ppQYxnrLba4f</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985670511\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-883220992 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 22:52:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6InRNcmQxRFZOMFZOZEtMMzVvSlBUNHc9PSIsInZhbHVlIjoiaHRFWDE2TUVibmFpV1A5ZkxMTlRMVnppTzN6VUtHK1lpVlhLc2dsY2h1YjUxWFVQNyswMzhYWTZtdTErRUNONUt0QUt1d3Y2Yyt3TDhFcXVEMTljaFo3ZENnZDVzRTVHL05TUXZTYTNrOVVxUjdEWU5zcXFYV2hKN0wzMGxBRkwiLCJtYWMiOiI4NDUwZTg1MzQ2ODAwYjliOGY4ZDM1MmEwOTQ1ZTk4MTlhZDQyYzI2ZGJiMzkzZDIzOWNjODljNDA3MjM4NDhhIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:52:27 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IjlTSXlDMFFIMmZhWHo5YXk5Zm05Ymc9PSIsInZhbHVlIjoiQXZsNWtuWHdoemRqRENjcExvZ0M5ZjZnZ1NkVUo5eFQrYXZOTzdIZGxQdU94M01RSXg2Vy85SWJrbmt3UEdaeDJTbHAzMWVVQmdrZWJDZGJncWZ3d1BrR1FwakJlMnNJRW50K0ozcUJ4MzBjUDVHaUhobkM5MEkrWEJSVFdCQ0YiLCJtYWMiOiJjODViOGE5NDI0MDZmNzAyMzFlN2ZjY2MwZThhODMzOWM5MzIzOGM3YzcyZGM0Zjc4MTQyMWNhNTA0Y2Q5OWJmIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:52:27 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6InRNcmQxRFZOMFZOZEtMMzVvSlBUNHc9PSIsInZhbHVlIjoiaHRFWDE2TUVibmFpV1A5ZkxMTlRMVnppTzN6VUtHK1lpVlhLc2dsY2h1YjUxWFVQNyswMzhYWTZtdTErRUNONUt0QUt1d3Y2Yyt3TDhFcXVEMTljaFo3ZENnZDVzRTVHL05TUXZTYTNrOVVxUjdEWU5zcXFYV2hKN0wzMGxBRkwiLCJtYWMiOiI4NDUwZTg1MzQ2ODAwYjliOGY4ZDM1MmEwOTQ1ZTk4MTlhZDQyYzI2ZGJiMzkzZDIzOWNjODljNDA3MjM4NDhhIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:52:27 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IjlTSXlDMFFIMmZhWHo5YXk5Zm05Ymc9PSIsInZhbHVlIjoiQXZsNWtuWHdoemRqRENjcExvZ0M5ZjZnZ1NkVUo5eFQrYXZOTzdIZGxQdU94M01RSXg2Vy85SWJrbmt3UEdaeDJTbHAzMWVVQmdrZWJDZGJncWZ3d1BrR1FwakJlMnNJRW50K0ozcUJ4MzBjUDVHaUhobkM5MEkrWEJSVFdCQ0YiLCJtYWMiOiJjODViOGE5NDI0MDZmNzAyMzFlN2ZjY2MwZThhODMzOWM5MzIzOGM3YzcyZGM0Zjc4MTQyMWNhNTA0Y2Q5OWJmIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:52:27 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883220992\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-852458445 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>url</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852458445\", {\"maxDepth\":0})</script>\n"}}