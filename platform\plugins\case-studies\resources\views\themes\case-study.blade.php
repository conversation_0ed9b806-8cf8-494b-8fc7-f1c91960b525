<div>
    <h3>{{ $caseStudy->name }}</h3>
    {!! Theme::breadcrumb()->render() !!}
</div>
<header>
    <h3>{{ $caseStudy->name }}</h3>
    <div>
        @if ($caseStudy->categories->isNotEmpty() && $caseStudy->categories->first())
            <span>
                <a href="{{ $caseStudy->categories->first()->url }}">{{ $caseStudy->categories->first()->name }}</a>
            </span>
        @endif
        <span>{{ $caseStudy->created_at->format('M d, Y') }}</span>
    </div>
</header>

@if ($caseStudy->image)
    <div class="case-study-featured-image">
        <img src="{{ RvMedia::getImageUrl($caseStudy->image) }}" alt="{{ $caseStudy->name }}" class="img-fluid">
    </div>
@endif

@if ($caseStudy->client_name || $caseStudy->client_logo)
    <div class="case-study-client-info">
        <h4>{{ __('Client') }}</h4>
        @if ($caseStudy->client_logo)
            <div class="client-logo">
                <img src="{{ RvMedia::getImageUrl($caseStudy->client_logo) }}" alt="{{ $caseStudy->client_name }}" class="client-logo-img">
            </div>
        @endif
        @if ($caseStudy->client_name)
            <h5>{{ $caseStudy->client_name }}</h5>
        @endif
    </div>
@endif

@if ($caseStudy->project_url)
    <div class="case-study-project-link">
        <a href="{{ $caseStudy->project_url }}" target="_blank" class="btn btn-primary">
            {{ __('View Project') }}
        </a>
    </div>
@endif

<div class='ck-content'>
    {!! BaseHelper::clean($caseStudy->content) !!}
</div>

@if ($caseStudy->challenge)
    <div class="case-study-challenge">
        <h4>{{ __('Challenge') }}</h4>
        <div class='ck-content'>
            {!! BaseHelper::clean($caseStudy->challenge) !!}
        </div>
    </div>
@endif

@if ($caseStudy->solution)
    <div class="case-study-solution">
        <h4>{{ __('Solution') }}</h4>
        <div class='ck-content'>
            {!! BaseHelper::clean($caseStudy->solution) !!}
        </div>
    </div>
@endif

@if ($caseStudy->results)
    <div class="case-study-results">
        <h4>{{ __('Results') }}</h4>
        <div class='ck-content'>
            {!! BaseHelper::clean($caseStudy->results) !!}
        </div>
    </div>
@endif

@if ($caseStudy->technologies)
    <div class="case-study-technologies">
        <h4>{{ __('Technologies Used') }}</h4>
        <ul>
            @foreach ($caseStudy->technologies as $technology)
                <li>{{ $technology[0]['value'] }}</li>
            @endforeach
        </ul>
    </div>
@endif

@if ($caseStudy->gallery)
    <div class="case-study-gallery">
        <h4>{{ __('Gallery') }}</h4>
        <div class="row">
            @foreach ($caseStudy->gallery as $image)
                @if($image)
                <div class="col-md-4 col-sm-6 mb-3">
                    <img src="{{ RvMedia::getImageUrl($image) }}" alt="{{ $caseStudy->name }}" class="img-fluid">
                </div>
                @endif
            @endforeach
        </div>
    </div>
@endif
