<?php

use Illuminate\Support\Arr;
use <PERSON><PERSON>qi\CaseStudies\Repositories\Interfaces\CaseStudyInterface;
use <PERSON>haqi\CaseStudies\Repositories\Interfaces\CaseStudyCategoryInterface;
use Illuminate\Support\Collection;
use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use Shaqi\Base\Supports\SortItemsWithChildrenHelper;

if (! function_exists('get_featured_case_studies')) {
    function get_featured_case_studies(int $limit = 5, array $with = ['slugable']): Collection
    {
        return app(CaseStudyInterface::class)->getFeatured($limit, $with);
    }
}

if (! function_exists('get_recent_case_studies')) {
    function get_recent_case_studies(int $limit = 5, int|string $categoryId = 0): Collection
    {
        return app(CaseStudyInterface::class)->getRecentCaseStudies($limit, $categoryId);
    }
}

if (! function_exists('get_related_case_studies')) {
    function get_related_case_studies(int|string $caseStudyId, int $limit = 3): Collection
    {
        return app(CaseStudyInterface::class)->getRelated($caseStudyId, $limit);
    }
}

if (! function_exists('get_case_studies_by_category')) {
    function get_case_studies_by_category(array|int|string $categoryId, int $paginate = 12, int $limit = 0): Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return app(CaseStudyInterface::class)->getByCategory($categoryId, $paginate, $limit);
    }
}

if (! function_exists('get_all_case_studies')) {
    function get_all_case_studies(int $perPage = 12, bool $active = true): Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return app(CaseStudyInterface::class)->getAllCaseStudies($perPage, $active);
    }
}

if (! function_exists('get_featured_case_study_categories')) {
    function get_featured_case_study_categories(int $limit = 5, array $with = ['slugable']): Collection
    {
        return app(CaseStudyCategoryInterface::class)->getFeaturedCategories($limit, $with);
    }
}

if (! function_exists('get_all_case_study_categories')) {
    function get_all_case_study_categories(array $condition = [], array $with = []): Collection
    {
        return app(CaseStudyCategoryInterface::class)->getAllCategories($condition, $with);
    }
}

if (! function_exists('get_popular_case_study_categories')) {
    function get_popular_case_study_categories(int $limit = 10): Collection
    {
        return app(CaseStudyCategoryInterface::class)->getPopularCategories($limit);
    }
}

if (! function_exists('get_case_study_categories')) {
    function get_case_study_categories(array $args = []): array
    {
        $indent = Arr::get($args, 'indent', '——');

        $repo = app(CaseStudyCategoryInterface::class);

        $categories = $repo->getCategories(Arr::get($args, 'select', ['*']), [
            'is_default' => 'DESC',
            'order' => 'ASC',
            'created_at' => 'DESC',
        ], Arr::get($args, 'condition', ['status' => BaseStatusEnum::PUBLISHED]));

        $categories = sort_item_with_children($categories);

        foreach ($categories as $category) {
            $depth = (int) $category->depth;
            $indentText = str_repeat($indent, $depth);
            $category->indent_text = $indentText;
        }

        return $categories;
    }
}

if (! function_exists('get_categories_with_children')) {
    function get_categories_with_children(): array
    {
        $categories = app(CaseStudyCategoryInterface::class)
            ->getAllCategoriesWithChildren(['status' => BaseStatusEnum::PUBLISHED], [], ['id', 'name', 'parent_id']);

        return app(SortItemsWithChildrenHelper::class)
            ->setChildrenProperty('child_cats')
            ->setItems($categories)
            ->sort();
    }
}

if (! function_exists('get_case_studies_by_slug')) {
    function get_case_studies_by_slug(string $slug, bool $active = true, array $with = [])
    {
        return app(CaseStudyInterface::class)->getFirstBy([
            'status' => $active ? BaseStatusEnum::PUBLISHED : null,
        ], ['*'], $with, function ($query) use ($slug) {
            return $query->whereHas('slugable', function ($subQuery) use ($slug) {
                $subQuery->where('key', $slug);
            });
        });
    }
}

if (! function_exists('get_case_study_category_by_slug')) {
    function get_case_study_category_by_slug(string $slug, bool $active = true, array $with = [])
    {
        return app(CaseStudyCategoryInterface::class)->getFirstBy([
            'status' => $active ? BaseStatusEnum::PUBLISHED : null,
        ], ['*'], $with, function ($query) use ($slug) {
            return $query->whereHas('slugable', function ($subQuery) use ($slug) {
                $subQuery->where('key', $slug);
            });
        });
    }
}
