/******/ (() => { // webpackBootstrap
var __webpack_exports__ = {};
/*!**********************************************************!*\
  !*** ./platform/themes/goalconversion/assets/js/main.js ***!
  \**********************************************************/
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
(function ($) {
  $(document).ready(function () {
    var _$$slick, _$$slick2, _$$slick3, _$$slick4, _$$slick5, _$$slick6, _$$slick7, _$$slick8, _rev$slick;
    ////////////////////////////////////////////////////

    ////////////////////////////////////////////////////
    // 03. Search Js
    $(".search-open-btn").on("click", function () {
      $(".search__popup").addClass("search-opened");
    });
    $(".search-close-btn").on("click", function () {
      $(".search__popup").removeClass("search-opened");
    });

    //========== HEADER ACTIVE STRATS ============= //
    var windowOn = $(window);
    windowOn.on('scroll', function () {
      var scroll = windowOn.scrollTop();
      if (scroll < 100) {
        $("#vl-header-sticky").removeClass("header-sticky");
      } else {
        $("#vl-header-sticky").addClass("header-sticky");
      }
    });

    //========== HEADER ACTIVE ENDS ============= //

    //========== PRICING AREA ============= //
    $("#ce-toggle").click(function (event) {
      $(".plan-toggle-wrap").toggleClass("active");
    });
    $("#ce-toggle").change(function () {
      if ($(this).is(":checked")) {
        $(".tab-content #yearly").hide();
        $(".tab-content #monthly").show();
      } else {
        $(".tab-content #yearly").show();
        $(".tab-content #monthly").hide();
      }
    });

    //========== MOBILE MENU STARTS ============= //
    var vlMenuWrap = $('.vl-mobile-menu-active > ul').clone();
    var vlSideMenu = $('.vl-offcanvas-menu nav');
    vlSideMenu.append(vlMenuWrap);
    if ($(vlSideMenu).find('.sub-menu, .vl-mega-menu').length != 0) {
      $(vlSideMenu).find('.sub-menu, .vl-mega-menu').parent().append('<button class="vl-menu-close"><i class="fas fa-chevron-right"></i></button>');
    }
    var sideMenuList = $('.vl-offcanvas-menu nav > ul > li button.vl-menu-close, .vl-offcanvas-menu nav > ul li.has-dropdown > a');
    $(sideMenuList).on('click', function (e) {
      console.log(e);
      e.preventDefault();
      if (!$(this).parent().hasClass('active')) {
        $(this).parent().addClass('active');
        $(this).siblings('.sub-menu, .vl-mega-menu').slideDown();
      } else {
        $(this).siblings('.sub-menu, .vl-mega-menu').slideUp();
        $(this).parent().removeClass('active');
      }
    });
    $(".vl-offcanvas-toggle").on('click', function () {
      $(".vl-offcanvas").addClass("vl-offcanvas-open");
      $(".vl-offcanvas-overlay").addClass("vl-offcanvas-overlay-open");
    });
    $(".vl-offcanvas-close-toggle,.vl-offcanvas-overlay").on('click', function () {
      $(".vl-offcanvas").removeClass("vl-offcanvas-open");
      $(".vl-offcanvas-overlay").removeClass("vl-offcanvas-overlay-open");
    });

    //========== MOBILE MENU ENDS ============= //

    {
      var animateElements = function animateElements() {
        $('.progressbar').each(function () {
          var elementPos = $(this).offset().top;
          var topOfWindow = $(window).scrollTop();
          var percent = $(this).find('.circle').attr('data-percent');
          var percentage = parseInt(percent, 10) / parseInt(100, 10);
          var animate = $(this).data('animate');
          if (elementPos < topOfWindow + $(window).height() - 10 && !animate) {
            $(this).data('animate', true);
            $(this).find('.circle').circleProgress({
              startAngle: -Math.PI / 2,
              value: percent / 100,
              size: 80,
              thickness: 5,
              emptyFill: "#E7E6F1",
              fill: {
                color: '#0778F9'
              }
            }).on('circle-animation-progress', function (event, progress, stepValue) {
              $(this).find('div').text((stepValue * 100).toFixed() + "%");
            }).stop();
          }
        });
      }; // Show animated elements
      animateElements();
      $(window).scroll(animateElements);
    }
    ;
    // sticky header active
    if ($("#header").length > 0) {
      $(window).on("scroll", function (event) {
        var scroll = $(window).scrollTop();
        if (scroll < 1) {
          $("#header").removeClass("sticky");
        } else {
          $("#header").addClass("sticky");
        }
      });
    }

    //Aos animation active
    AOS.init({
      offset: 0,
      duration: 400,
      easing: "ease-in-out",
      anchorPlacement: "top-bottom",
      disable: "mobile",
      once: false
    });

    //Video poppup
    if ($(".play-btn").length > 0) {
      $(".play-btn").magnificPopup({
        type: "iframe"
      });
    }
    ;

    // page-progress
    var progressPath = document.querySelector(".progress-wrap path");
    var pathLength = progressPath.getTotalLength();
    progressPath.style.transition = progressPath.style.WebkitTransition = "none";
    progressPath.style.strokeDasharray = pathLength + " " + pathLength;
    progressPath.style.strokeDashoffset = pathLength;
    progressPath.getBoundingClientRect();
    progressPath.style.transition = progressPath.style.WebkitTransition = "stroke-dashoffset 10ms linear";
    var updateProgress = function updateProgress() {
      var scroll = $(window).scrollTop();
      var height = $(document).height() - $(window).height();
      var progress = pathLength - scroll * pathLength / height;
      progressPath.style.strokeDashoffset = progress;
    };
    updateProgress();
    $(window).scroll(updateProgress);
    var offset = 50;
    var duration = 550;
    jQuery(window).on("scroll", function () {
      if (jQuery(this).scrollTop() > offset) {
        jQuery(".progress-wrap").addClass("active-progress");
      } else {
        jQuery(".progress-wrap").removeClass("active-progress");
      }
    });
    jQuery(".progress-wrap").on("click", function (event) {
      event.preventDefault();
      jQuery("html, body").animate({
        scrollTop: 0
      }, duration);
      return false;
    });

    //product colors
    var colors = $(".accordion1 .accordion-item");
    colors.on("click", function () {
      $(".accordion1 .accordion-item").removeClass("active");
      $(this).addClass("active");
    });

    //product colors
    var colors2 = $(".accordion2 .accordion-item");
    colors2.on("click", function () {
      $(".accordion2 .accordion-item").removeClass("active");
      $(this).addClass("active");
    });

    //select colors
    var select1 = $("select1");
    select1.on("click", function () {
      $("select1").removeClass("active");
      $(this).addClass("active");
    });

    //tes1 active
    var tes1 = $(".controls li");
    tes1.on("click", function () {
      $(".controls li").removeClass("active");
      $(this).addClass("active");
    });
    $("#ce-toggle1").click(function (event) {
      $(".plan-toggle-wrap1").toggleClass("active");
    });
    $("#ce-toggle1").change(function () {
      if ($(this).is(":checked")) {
        $(".tab-content #yearly1").hide();
        $(".tab-content #monthly1").show();
      } else {
        $(".tab-content #yearly1").show();
        $(".tab-content #monthly1").hide();
      }
    });
    $(".work1-slider").slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: true,
      autoplay: false,
      autoplaySpeed: 2000,
      loop: true,
      focusOnSelect: true,
      infinite: true,
      prevArrow: $('.work1-prev-arrow'),
      nextArrow: $('.work1-next-arrow')
    });

    //-- testimonial 6 ---
    $(".tes6-slider").slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: true,
      autoplay: false,
      autoplaySpeed: 2000,
      loop: true,
      focusOnSelect: true,
      infinite: true,
      prevArrow: $('.tes6-prev-arrow'),
      nextArrow: $('.tes6-next-arrow')
    });

    //-- testimonial 6 ---
    $(".tes5-slider").slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: true,
      autoplay: false,
      autoplaySpeed: 2000,
      loop: true,
      focusOnSelect: true,
      infinite: true,
      prevArrow: $('.tes5-prev-arrow'),
      nextArrow: $('.tes5-next-arrow')
    });

    //-- testimonial 6 ---
    $(".tes4-slider").slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: false,
      dots: true,
      autoplay: false,
      autoplaySpeed: 2000,
      loop: true,
      focusOnSelect: true,
      infinite: true,
      prevArrow: $('.tes5-prev-arrow'),
      nextArrow: $('.tes5-next-arrow')
    });

    //-- testimonial 9 ---
    $(".tes9-slider").slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: true,
      dots: false,
      autoplay: false,
      autoplaySpeed: 2000,
      loop: true,
      focusOnSelect: true,
      infinite: true,
      prevArrow: $('.tes9-prev-arrow'),
      nextArrow: $('.tes9-next-arrow'),
      vertical: true
    });

    //-- testimonial 9 ---
    $(".tes8-slider").slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: true,
      dots: false,
      autoplay: false,
      autoplaySpeed: 2000,
      loop: true,
      focusOnSelect: true,
      infinite: true,
      prevArrow: $('.tes8-prev-arrow'),
      nextArrow: $('.tes8-next-arrow'),
      vertical: true
    });

    //testimonial 2 slider
    $(".tes2-slider").slick((_$$slick = {
      margin: "30",
      slidesToShow: 3,
      arrows: false,
      centerMode: true,
      dots: false,
      loop: true
    }, _defineProperty(_$$slick, "centerMode", false), _defineProperty(_$$slick, "draggable", true), _defineProperty(_$$slick, "autoplay", true), _defineProperty(_$$slick, "autoplaySpeed", 4000), _defineProperty(_$$slick, "fade", false), _defineProperty(_$$slick, "fadeSpeed", 1000), _defineProperty(_$$slick, "responsive", [{
      breakpoint: 769,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }]), _$$slick));

    //Team 10 slider
    $(".team10-slider").slick((_$$slick2 = {
      margin: "30",
      slidesToShow: 3,
      arrows: true,
      prevArrow: $('.team10-prev-arrow'),
      nextArrow: $('.team10-next-arrow'),
      centerMode: true,
      dots: false,
      loop: true
    }, _defineProperty(_$$slick2, "centerMode", false), _defineProperty(_$$slick2, "draggable", true), _defineProperty(_$$slick2, "autoplay", true), _defineProperty(_$$slick2, "autoplaySpeed", 4000), _defineProperty(_$$slick2, "fade", false), _defineProperty(_$$slick2, "fadeSpeed", 1000), _defineProperty(_$$slick2, "responsive", [{
      breakpoint: 769,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }]), _$$slick2));

    //Team 8 slider
    $(".team8-slider").slick((_$$slick3 = {
      margin: "30",
      slidesToShow: 4,
      arrows: true,
      prevArrow: $('.team8-prev-arrow'),
      nextArrow: $('.team8-next-arrow'),
      centerMode: true,
      dots: false,
      loop: true
    }, _defineProperty(_$$slick3, "centerMode", false), _defineProperty(_$$slick3, "draggable", true), _defineProperty(_$$slick3, "autoplay", true), _defineProperty(_$$slick3, "autoplaySpeed", 4000), _defineProperty(_$$slick3, "fade", false), _defineProperty(_$$slick3, "fadeSpeed", 1000), _defineProperty(_$$slick3, "responsive", [{
      breakpoint: 769,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }]), _$$slick3));

    //Team 10 slider
    $(".tes10-slider").slick((_$$slick4 = {
      margin: "30",
      slidesToShow: 3,
      arrows: true,
      prevArrow: $('.tes10-prev-arrow'),
      nextArrow: $('.tes10-next-arrow'),
      centerMode: true,
      dots: false,
      loop: true
    }, _defineProperty(_$$slick4, "centerMode", false), _defineProperty(_$$slick4, "draggable", true), _defineProperty(_$$slick4, "autoplay", true), _defineProperty(_$$slick4, "autoplaySpeed", 4000), _defineProperty(_$$slick4, "fade", false), _defineProperty(_$$slick4, "fadeSpeed", 1000), _defineProperty(_$$slick4, "responsive", [{
      breakpoint: 769,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }]), _$$slick4));

    // brands slider 9
    $(".brands9-slider").slick((_$$slick5 = {
      margin: "30",
      slidesToShow: 7,
      arrows: false,
      centerMode: true,
      dots: false,
      loop: true
    }, _defineProperty(_$$slick5, "centerMode", false), _defineProperty(_$$slick5, "draggable", true), _defineProperty(_$$slick5, "autoplay", true), _defineProperty(_$$slick5, "autoplaySpeed", 2000), _defineProperty(_$$slick5, "fade", false), _defineProperty(_$$slick5, "fadeSpeed", 1000), _defineProperty(_$$slick5, "responsive", [{
      breakpoint: 769,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 4
      }
    }, {
      breakpoint: 480,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 2
      }
    }]), _$$slick5));

    //testimonial 2 slider
    $(".tes7-slider").slick((_$$slick6 = {
      margin: "30",
      slidesToShow: 3,
      arrows: false,
      centerMode: true,
      dots: true,
      loop: true
    }, _defineProperty(_$$slick6, "centerMode", false), _defineProperty(_$$slick6, "draggable", true), _defineProperty(_$$slick6, "autoplay", true), _defineProperty(_$$slick6, "autoplaySpeed", 4000), _defineProperty(_$$slick6, "fade", false), _defineProperty(_$$slick6, "fadeSpeed", 1000), _defineProperty(_$$slick6, "responsive", [{
      breakpoint: 769,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }]), _$$slick6));
    $('.istagram-feed3-slider').owlCarousel({
      loop: true,
      margin: 10,
      responsiveClass: true,
      nav: false,
      responsive: {
        0: {
          items: 1,
          nav: false
        },
        600: {
          items: 3,
          nav: false
        },
        1000: {
          items: 5.5,
          nav: false,
          loop: false
        }
      }
    });
    $('.hero7-slider').owlCarousel({
      loop: true,
      margin: 10,
      responsiveClass: true,
      nav: false,
      responsive: {
        0: {
          items: 1,
          nav: false
        },
        600: {
          items: 3,
          nav: false
        },
        1000: {
          items: 5,
          nav: false,
          loop: false
        }
      }
    });

    //testimonial 3 slider
    $(".tes3-slider").slick((_$$slick7 = {
      margin: "30",
      slidesToShow: 1,
      arrows: false,
      centerMode: true,
      dots: false,
      loop: true
    }, _defineProperty(_$$slick7, "centerMode", false), _defineProperty(_$$slick7, "draggable", true), _defineProperty(_$$slick7, "autoplay", true), _defineProperty(_$$slick7, "autoplaySpeed", 4000), _defineProperty(_$$slick7, "fade", false), _defineProperty(_$$slick7, "fadeSpeed", 1000), _defineProperty(_$$slick7, "dots", true), _defineProperty(_$$slick7, "responsive", [{
      breakpoint: 769,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }]), _$$slick7));

    //testimonial 11 slider
    $(".tes11-slider").slick((_$$slick8 = {
      margin: "30",
      slidesToShow: 1,
      arrows: false,
      centerMode: true,
      dots: false,
      loop: true
    }, _defineProperty(_$$slick8, "centerMode", false), _defineProperty(_$$slick8, "draggable", true), _defineProperty(_$$slick8, "autoplay", true), _defineProperty(_$$slick8, "autoplaySpeed", 4000), _defineProperty(_$$slick8, "fade", false), _defineProperty(_$$slick8, "fadeSpeed", 1000), _defineProperty(_$$slick8, "dots", true), _defineProperty(_$$slick8, "responsive", [{
      breakpoint: 769,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        arrows: false,
        centerMode: false,
        centerPadding: "40px",
        slidesToShow: 1
      }
    }]), _$$slick8));

    // SLIDER //
    var rev = $('.rev_slider');
    rev.on('init', function (event, slick, currentSlide) {
      var cur = $(slick.$slides[slick.currentSlide]),
        next = cur.next(),
        prev = cur.prev();
      prev.addClass('slick-sprev');
      next.addClass('slick-snext');
      cur.removeClass('slick-snext').removeClass('slick-sprev');
      slick.$prev = prev;
      slick.$next = next;
    }).on('beforeChange', function (event, slick, currentSlide, nextSlide) {
      var cur = $(slick.$slides[nextSlide]);
      slick.$prev.removeClass('slick-sprev');
      slick.$next.removeClass('slick-snext');
      next = cur.next(), prev = cur.prev();
      prev.prev();
      prev.next();
      prev.addClass('slick-sprev');
      next.addClass('slick-snext');
      slick.$prev = prev;
      slick.$next = next;
      cur.removeClass('slick-next').removeClass('slick-sprev');
    });
    rev.slick((_rev$slick = {
      speed: 1000,
      arrows: true,
      dots: false,
      focusOnSelect: true,
      prevArrow: '<button class="prev-next"><i class="fa-solid fa-angle-left"></i></button>',
      nextArrow: '<button class="next-prev"> <i class="fa-solid fa-angle-right"></i></button>',
      infinite: true,
      centerMode: true,
      slidesPerRow: 1,
      slidesToShow: 5,
      slidesToScroll: 1,
      centerPadding: '0',
      swipe: true,
      autoplaySpeed: 2500
    }, _defineProperty(_rev$slick, "speed", 1500), _defineProperty(_rev$slick, "autoplay", false), _defineProperty(_rev$slick, "customPaging", function customPaging(slider, i) {
      return '';
    }), _rev$slick));
    $('.cs_hover_active').hover(function () {
      $(this).addClass('active').siblings().removeClass('active');
    });
  });

  /* Text Effect Animation */
  if ($('.text-anime-style-1').length) {
    var staggerAmount = 0.05,
      translateXValue = 0,
      delayValue = 0.5,
      animatedTextElements = document.querySelectorAll('.text-anime-style-1');
    animatedTextElements.forEach(function (element) {
      var animationSplitText = new SplitText(element, {
        type: "chars, words"
      });
      gsap.from(animationSplitText.words, {
        duration: 1,
        delay: delayValue,
        x: 20,
        autoAlpha: 0,
        stagger: staggerAmount,
        scrollTrigger: {
          trigger: element,
          start: "top 85%"
        }
      });
    });
  }
  if ($('.text-anime-style-2').length) {
    var _staggerAmount = 0.05,
      _translateXValue = 20,
      _delayValue = 0.5,
      easeType = "power2.out",
      _animatedTextElements = document.querySelectorAll('.text-anime-style-2');
    _animatedTextElements.forEach(function (element) {
      var animationSplitText = new SplitText(element, {
        type: "chars, words"
      });
      gsap.from(animationSplitText.chars, {
        duration: 1,
        delay: _delayValue,
        x: _translateXValue,
        autoAlpha: 0,
        stagger: _staggerAmount,
        ease: easeType,
        scrollTrigger: {
          trigger: element,
          start: "top 85%"
        }
      });
    });
  }
  if ($('.text-anime-style-3').length) {
    var _animatedTextElements2 = document.querySelectorAll('.text-anime-style-3');
    _animatedTextElements2.forEach(function (element) {
      //Reset if needed
      if (element.animation) {
        element.animation.progress(1).kill();
        element.split.revert();
      }
      element.split = new SplitText(element, {
        type: "lines,words,chars",
        linesClass: "split-line"
      });
      gsap.set(element, {
        perspective: 400
      });
      gsap.set(element.split.chars, {
        opacity: 0,
        x: "50"
      });
      element.animation = gsap.to(element.split.chars, {
        scrollTrigger: {
          trigger: element,
          start: "top 95%"
        },
        x: "0",
        y: "0",
        rotateX: "0",
        opacity: 1,
        duration: 1,
        ease: Back.easeOut,
        stagger: 0.02
      });
    });
  }

  // btn_theme
  $(function () {
    $('.btn_theme3').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme4').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme5').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme6').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme7').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme8').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme9').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme10').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme11').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });

  // btn_theme
  $(function () {
    $('.btn_theme12').on('mouseenter', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    }).on('mouseout', function (e) {
      var parentOffset = $(this).offset(),
        relX = e.pageX - parentOffset.left,
        relY = e.pageY - parentOffset.top;
      $(this).find('span').css({
        top: relY,
        left: relX
      });
    });
  });
  $('select').niceSelect();
  $('.clients').slick({
    dots: false,
    loop: true,
    infinite: true,
    speed: 300,
    slidesToShow: 8,
    slidesToScroll: 2,
    autoplay: true,
    autoplaySpeed: 2000,
    responsive: [{
      breakpoint: 1024,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 2,
        infinite: true,
        dots: false
      }
    }, {
      breakpoint: 600,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1
      }
    }, {
      breakpoint: 480,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1
      }
    }
    // You can unslick at a given breakpoint now by adding:
    // settings: "unslick"
    // instead of a settings object
    ]
  });
})(jQuery);

// SWIPER SLIDER //
document.addEventListener("DOMContentLoaded", function () {
  var swiper3 = new Swiper(".swiper-thumb2", {
    spaceBetween: 10,
    slidesPerView: 6,
    freeMode: true,
    watchSlidesProgress: true,
    autoplay: {
      delay: 2500,
      disableOnInteraction: false
    }
  });
  var swiper4 = new Swiper(".swiper-testimonial-2", {
    spaceBetween: 10,
    loop: true,
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev"
    },
    autoplay: {
      delay: 2500,
      disableOnInteraction: false
    },
    thumbs: {
      swiper: swiper3
    }
  });
});
document.addEventListener("DOMContentLoaded", function () {
  // Safe checks for elements to avoid errors
  var categories = document.querySelectorAll(".category");

  // Check if categories exist before adding event listeners
  if (categories.length > 0) {
    categories.forEach(function (category) {
      category.addEventListener("click", function () {
        categories.forEach(function (cat) {
          return cat.classList.remove("active");
        });
        category.classList.add("active");
      });
    });
  }
});
var slider = document.getElementById('balance-slider');
var selectedValue = document.getElementById('selectedValue');
if (slider && selectedValue) {
  // Function to update the background gradient
  var updateSliderBackground = function updateSliderBackground() {
    var value = slider.value;
    var max = slider.max;
    var percentage = value / max * 100;
    slider.style.background = "linear-gradient(to right, Navy ".concat(percentage, "%, #e0e0e0 ").concat(percentage, "%)");
  }; // Event listener for slider input
  slider.addEventListener('input', function () {
    selectedValue.textContent = this.value;
    updateSliderBackground();
  });

  // Initialize the slider background on page load
  updateSliderBackground();
}
/******/ })()
;
//# sourceMappingURL=main.js.map