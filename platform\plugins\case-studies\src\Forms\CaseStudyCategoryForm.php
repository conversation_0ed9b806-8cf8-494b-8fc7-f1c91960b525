<?php

namespace <PERSON><PERSON>qi\CaseStudies\Forms;

use Shaqi\Base\Forms\FieldOptions\DescriptionFieldOption;
use Shaqi\Base\Forms\FieldOptions\HiddenFieldOption;
use Shaqi\Base\Forms\FieldOptions\IsFeaturedFieldOption;
use Shaqi\Base\Forms\FieldOptions\NameFieldOption;
use Shaqi\Base\Forms\FieldOptions\OnOffFieldOption;
use Shaqi\Base\Forms\FieldOptions\SelectFieldOption;
use Shaqi\Base\Forms\FieldOptions\StatusFieldOption;
use Shaqi\Base\Forms\Fields\HiddenField;
use Shaqi\Base\Forms\Fields\OnOffField;
use Shaqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Forms\Fields\TextareaField;
use Shaqi\Base\Forms\Fields\TextField;
use Shaqi\Base\Forms\FormAbstract;
use Shaqi\CaseStudies\Http\Requests\CaseStudyCategoryRequest;
use S<PERSON>qi\CaseStudies\Models\CaseStudyCategory;

class CaseStudyCategoryForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(CaseStudyCategory::class)
            ->setValidatorClass(CaseStudyCategoryRequest::class)
            ->add(
                'order',
                HiddenField::class,
                HiddenFieldOption::make()
                    ->value(function () {
                        if ($this->getModel()->exists) {
                            return $this->getModel()->order;
                        }

                        return CaseStudyCategory::query()
                                ->whereIn('parent_id', [0, null])
                                ->latest('order')
                                ->value('order') + 1;
                    })
            )
            ->add(
                'parent_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('core/base::forms.parent'))
                    ->choices(
                        CaseStudyCategory::query()
                            ->wherePublished()
                            ->where('parent_id', 0)
                            ->pluck('name', 'id')
                            ->prepend(trans('plugins/case-studies::categories.form.none'), 0)
                            ->toArray()
                    )
                    ->selected(old('parent_id', $this->getModel()->parent_id))
                    ->addAttribute('class', 'select-search-full')
                    ->when($this->getModel()->getKey(), function (SelectFieldOption $fieldOption) {
                        return $fieldOption->disabled();
                    })
            )
            ->add('name', TextField::class, NameFieldOption::make()->required()->maxLength(120))
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add(
                'is_featured',
                OnOffField::class,
                IsFeaturedFieldOption::make()
            )
            ->add(
                'is_default',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/case-studies::categories.form.is_default'))
                    ->defaultValue(false)
            )
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->setBreakFieldPoint('status');
    }
}
