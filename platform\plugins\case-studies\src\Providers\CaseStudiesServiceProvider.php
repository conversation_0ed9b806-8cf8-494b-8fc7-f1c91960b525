<?php

namespace Shaqi\CaseStudies\Providers;

use Shaqi\ACL\Models\User;
use Shaqi\Api\Facades\ApiHelper;
use Shaqi\Base\Facades\Assets;
use Shaqi\Base\Facades\DashboardMenu;
use Shaqi\Base\Facades\PanelSectionManager;
use Shaqi\Base\PanelSections\PanelSectionItem;
use Shaqi\Base\Supports\DashboardMenuItem;
use Shaqi\Base\Supports\ServiceProvider;
use Shaqi\Base\Traits\LoadAndPublishDataTrait;
use Shaqi\CaseStudies\Models\CaseStudy;
use Shaqi\CaseStudies\Models\CaseStudyCategory;
use Shaqi\CaseStudies\Repositories\Eloquent\CaseStudyRepository;
use Shaqi\CaseStudies\Repositories\Eloquent\CaseStudyCategoryRepository;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyInterface;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyCategoryInterface;
use Shaqi\DataSynchronize\PanelSections\ExportPanelSection;
use Shaqi\DataSynchronize\PanelSections\ImportPanelSection;
use Shaqi\Language\Facades\Language;
use Shaqi\LanguageAdvanced\Supports\LanguageAdvancedManager;
use Shaqi\PluginManagement\Events\DeactivatedPlugin;
use Shaqi\PluginManagement\Events\RemovedPlugin;
use Shaqi\SeoHelper\Facades\SeoHelper;
use Shaqi\Setting\PanelSections\SettingOthersPanelSection;
use Shaqi\Shortcode\View\View;
use Shaqi\Slug\Facades\SlugHelper;
use Shaqi\Slug\Models\Slug;
use Shaqi\Theme\Events\ThemeRoutingBeforeEvent;
use Shaqi\Theme\Facades\SiteMapManager;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\AliasLoader;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;

/**
 * @since 18/07/2025 12:00 AM
 */
class CaseStudiesServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(CaseStudyInterface::class, function () {
            return new CaseStudyRepository(new CaseStudy());
        });

        $this->app->bind(CaseStudyCategoryInterface::class, function () {
            return new CaseStudyCategoryRepository(new CaseStudyCategory());
        });
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/case-studies')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'general'])
            ->loadAndPublishViews()
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadMigrations()
            ->publishAssets();

        if (class_exists('ApiHelper') && ApiHelper::enabled()) {
            $this->loadRoutes(['api']);
        }

        $this->app->register(EventServiceProvider::class);

        // Load assets for admin
        if (is_in_admin()) {
            Assets::addScriptsDirectly('vendor/core/plugins/case-studies/js/case-studies.js')
                ->addStylesDirectly('vendor/core/plugins/case-studies/css/case-studies.css');
        }

        Event::listen(RouteMatched::class, function (): void {
            dashboard_menu()->registerItem([
                'id' => 'cms-plugins-case-studies',
                'priority' => 5,
                'parent_id' => 'cms-plugins',
                'name' => 'plugins/case-studies::base.menu_name',
                'icon' => 'fas fa-file-alt',
                'url' => route('case-studies.index'),
                'permissions' => ['case-studies.index'],
            ])
            ->registerItem([
                'id' => 'cms-plugins-case-studies-post',
                'priority' => 1,
                'parent_id' => 'cms-plugins-case-studies',
                'name' => 'plugins/case-studies::case-studies.menu',
                'url' => route('case-studies.index'),
                'permissions' => ['case-studies.index'],
            ])
            ->registerItem([
                'id' => 'cms-plugins-case-studies-categories',
                'priority' => 2,
                'parent_id' => 'cms-plugins-case-studies',
                'name' => 'plugins/case-studies::categories.menu',
                'url' => route('case-study-categories.index'),
                'permissions' => ['case-study-categories.index'],
            ]);
        });

        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-case-studies')
                        ->priority(5)
                        ->parentId('cms-plugins')
                        ->name('plugins/case-studies::base.menu_name')
                        ->icon('fas fa-file-alt')
                        ->url(route('case-studies.index'))
                        ->permissions(['case-studies.index'])
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-case-studies-post')
                        ->priority(1)
                        ->parentId('cms-plugins-case-studies')
                        ->name('plugins/case-studies::case-studies.menu')
                        ->url(route('case-studies.index'))
                        ->permissions(['case-studies.index'])
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-case-studies-categories')
                        ->priority(2)
                        ->parentId('cms-plugins-case-studies')
                        ->name('plugins/case-studies::categories.menu')
                        ->url(route('case-study-categories.index'))
                        ->permissions(['case-study-categories.index'])
                );
        });

 

        SlugHelper::registerModule(CaseStudy::class, 'Case Studies');
        SlugHelper::registerModule(CaseStudyCategory::class, 'Case Study Categories');
        SlugHelper::setPrefix(CaseStudy::class, 'case-study', true);
        SlugHelper::setPrefix(CaseStudyCategory::class, 'case-study-category', true);

        Event::listen([DeactivatedPlugin::class, RemovedPlugin::class], function ($event): void {
            if ($event->plugin->getId() === 'shaqi/case-studies') {
                PanelSectionManager::default()->removeItem('case-studies-settings');
            }
        });

        Event::listen(ThemeRoutingBeforeEvent::class, function (): void {
            SiteMapManager::registerKey(['case-studies', 'case-study-categories']);
        });

        $this->app->booted(function (): void {
            SeoHelper::registerModule([CaseStudy::class, CaseStudyCategory::class]);

            $configKey = 'packages.revision.general.supported';
            config()->set($configKey, array_merge(config($configKey, []), [CaseStudy::class]));

            $this->app->register(HookServiceProvider::class);
        });

        if (function_exists('shortcode')) {
            view()->composer([
                'plugins/case-studies::themes.case-study',
                'plugins/case-studies::themes.category',
            ], function (View $view): void {
                $view->withShortcodes();
            });
        }
    }
}
