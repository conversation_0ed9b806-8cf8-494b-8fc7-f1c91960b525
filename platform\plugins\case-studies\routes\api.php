<?php

use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => 'api',
    'prefix' => 'api/v1',
    'namespace' => 'Shaqi\CaseStudies\Http\Controllers\API',
], function (): void {
    Route::get('search', 'CaseStudyController@getSearch');
    Route::get('case-studies', 'CaseStudyController@index');
    Route::get('case-study-categories', 'CaseStudyCategoryController@index');

    Route::get('case-studies/filters', 'CaseStudyController@getFilters');
    Route::get('case-studies/{slug}', 'CaseStudyController@findBySlug');
    Route::get('case-study-categories/filters', 'CaseStudyCategoryController@getFilters');
    Route::get('case-study-categories/{slug}', 'CaseStudyCategoryController@findBySlug');
});
