<?php

namespace Shaqi\CaseStudies\Http\Controllers\API;

use <PERSON>haqi\Api\Http\Controllers\BaseController;
use <PERSON>haqi\CaseStudies\Http\Resources\CategoryResource;
use <PERSON>haqi\CaseStudies\Models\CaseStudyCategory;
use <PERSON><PERSON>qi\CaseStudies\Repositories\Interfaces\CaseStudyCategoryInterface;
use Illuminate\Http\Request;

class CaseStudyCategoryController extends BaseController
{
    public function __construct(protected CaseStudyCategoryInterface $categoryRepository)
    {
    }

    /**
     * List case study categories
     *
     * @group Case Studies
     */
    public function index(Request $request)
    {
        $data = CaseStudyCategory::query()
            ->wherePublished()
            ->orderByDesc('created_at')
            ->with(['slugable'])
            ->paginate($request->integer('per_page', 10) ?: 10);

        return $this
            ->httpResponse()
            ->setData(CategoryResource::collection($data))
            ->toApiResponse();
    }

    /**
     * Get category by slug
     *
     * @group Case Studies
     */
    public function findBySlug(string $slug, Request $request)
    {
        $category = get_case_study_category_by_slug($slug, true);

        if (! $category) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage('Not found');
        }

        return $this
            ->httpResponse()
            ->setData(new CategoryResource($category))
            ->toApiResponse();
    }

    /**
     * Get category filters
     *
     * @group Case Studies
     */
    public function getFilters(Request $request)
    {
        $data = CaseStudyCategory::query()
            ->wherePublished()
            ->orderBy('order')
            ->orderByDesc('created_at')
            ->with(['slugable'])
            ->get();

        return $this
            ->httpResponse()
            ->setData(CategoryResource::collection($data))
            ->toApiResponse();
    }
}
