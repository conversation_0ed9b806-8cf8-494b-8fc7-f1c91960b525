{"__meta": {"id": "X31201c5e8e416f91e3f1c8f474b508ad", "datetime": "2025-07-18 22:47:01", "utime": **********.413071, "method": "GET", "uri": "/gc/lgn/settings/license/verify?verified=true", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.258123, "end": **********.41311, "duration": 1.154987096786499, "duration_str": "1.15s", "measures": [{"label": "Booting", "start": **********.258123, "relative_start": 0, "end": **********.333945, "relative_end": **********.333945, "duration": 1.075822114944458, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.333971, "relative_start": 1.07584810256958, "end": **********.413113, "relative_end": 3.0994415283203125e-06, "duration": 0.07914209365844727, "duration_str": "79.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 41512232, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET gc/lgn/settings/license/verify", "middleware": "web, core, auth", "as": "settings.license.verify.index", "permission": false, "controller": "\\Shaqi\\ShaqiActivator\\Http\\Controllers\\ShaqiActivatorController@getVerifyLicense", "namespace": "Shaqi\\Setting\\Http\\Controllers", "prefix": "gc/lgn/settings/license", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fplugins%2Fmagic%2Fsrc%2FHttp%2FControllers%2FShaqiActivatorController.php&line=36\" onclick=\"\">platform/plugins/magic/src/Http/Controllers/ShaqiActivatorController.php:36-45</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01226, "accumulated_duration_str": "12.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.387232, "duration": 0.01226, "duration_str": "12.26ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "url": "[]"}, "request": {"path_info": "/gc/lgn/settings/license/verify", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>verified</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ing4WUE2U2NBS0M2WHJ0TU5CU2hqRVE9PSIsInZhbHVlIjoiazJLZ1RtdFRTOWMzMFE0K3FmbU9OQnNMbjFPOHdWVHpoQWg1L2FseDhwRU9EdmdRU2IxSm5BeVNHdzdyRlErMmFDS1BqeGVvSUVWZVFleE1IY0QrRWtVN0I2bGZoZWlkdHEwczQ4QzZ6Q012K1JkbVRtOHFiMFVSTlFrOEVQVHAiLCJtYWMiOiI5ZWJkNGI0YjljZWE0NDdlYjRlNTY0YzQxZWQzNGMzNWY1ZjUzYzVlYmFhM2I1OWJkNzE3M2E3N2E0YTE2ODc3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://goalconversion.gc/gc/lgn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; _ga_FSKD72EXSB=GS2.1.s1752869141$o43$g1$t1752874702$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJqeUJpUklyZGNINDVwa09NNnJiTGc9PSIsInZhbHVlIjoiZ1picmMxRWU5V2xQb1haQVdaSEtsSVo1dGtFbnAzK3E4T0N6VVU1TThlck9Pb01hODBlQzVCeld0N2UrS29uMFlpSGdqZyttdzQ0K20rcE55WHhCbVZhb05BaXBSVHNEY1ZMa05kYThpbS9PUURhUWZuVkpsT1JzZW1DZ2ViR1NxWVg5aVZDRktqTzF6NkpqSHVKRmtwNWlhdHlNQVp3aWJmWU8rZnBCVURubVVsMjcxTjFvV2VZd01ibTlZc2xKcWRVMHJWNHdwNG1iaThsR1JLWTlEMDRkRGJUQjVlTlZPanpsL1FPMHlMUT0iLCJtYWMiOiIzZTBiNWRhYjZiNmFjOWZlZTVhNDVlNDdkNjRlZGI2NTA0MTZhYjdhZmFjOWJmYTBjYjMwNzA1YmQyZGE3YmUxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ing4WUE2U2NBS0M2WHJ0TU5CU2hqRVE9PSIsInZhbHVlIjoiazJLZ1RtdFRTOWMzMFE0K3FmbU9OQnNMbjFPOHdWVHpoQWg1L2FseDhwRU9EdmdRU2IxSm5BeVNHdzdyRlErMmFDS1BqeGVvSUVWZVFleE1IY0QrRWtVN0I2bGZoZWlkdHEwczQ4QzZ6Q012K1JkbVRtOHFiMFVSTlFrOEVQVHAiLCJtYWMiOiI5ZWJkNGI0YjljZWE0NDdlYjRlNTY0YzQxZWQzNGMzNWY1ZjUzYzVlYmFhM2I1OWJkNzE3M2E3N2E0YTE2ODc3IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IkZKWDRpYWM5TWdGckl0N0trTmxQdkE9PSIsInZhbHVlIjoiTXZEWmFENUNteDdtbUxlTk9hdFlOTDJtajFZcCtwMEwvbUxSekh2UXlYRkwzWG9YWTdCZEIxUkx3ZklUL2NJZ1A3Rld6bVEvL3haOWRObmJUV2pLdE9RZy9OL0xjVzUrc1ZYZUVpR1hFU1o0OU52Ty96WGphN0FPVHl2WkhPaEIiLCJtYWMiOiJkYmQwZWE0M2U4ZjllZmQ1YzAyZmI1YzhkNDlhNzU4N2Y0OThhNWE3ZTIxYTc4ZmU5MTM2N2JiNTcyOTUwODc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-450635825 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|93AZBoOuq93wZdZjYcXin3OIxaW8YfC1pd64jsSLusTXT7oOXJKvnP4Pzefq|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pfgazGmSZFAgAHgBDhcfMpRcFY48ppQYxnrLba4f</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450635825\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-850783250 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 22:47:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6Ik1TRTR1T3U5L1Fzc1IybzVLYVBUU2c9PSIsInZhbHVlIjoiSE1mdDVxS1pHZ1BTTkVpanUweFVrS0hjVjJkQ3lJOTRwejBXU1J0TGQ4VWdQdm44WkwxQkVxY1FTbEZIWDRWSmNNQnllVC82bkQ5dlJtUEVFcU1JRmc1dnZoNzNIbTE5V2FuWWRXYXE4SHdJbmFuZ2hLdWZlQnFiZ25lU2MxZ2QiLCJtYWMiOiIwMzlkZTc0YzQyNjZjMTkyMDIzMWExN2EyNjYxYmQ0NTRmMDcxYWY3MTNjMGQ3MDdhMTYzZTQ4ZjIyNjRlNTVlIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:47:01 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6ImlXRk5HKy8vTW1BRnNPYVlybVdJQ1E9PSIsInZhbHVlIjoiclZoYU5mdGhhUExzYXNkSzVmWGVDQ080aEZIZHp1SDNhZkdJaUQxMzVVUERFMExjWkVoSFNZQ1RsRDEycGY4emRNcm5yOHdwaHdQbE9Rc3JDZHM3U21XL29VMWpzd2VMeVo3UnFwM29KdXRvRG4vTHFxeXZzR0MyZWg1Mkk1elYiLCJtYWMiOiIyYTk3OTY4YjQxM2M1MGJmZWUxODFkNzgyZDQ1MGUzODczZTVhZTY5MGVlYjFhNGRiZTAyMGNlNmUzNWJiODM5IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:47:01 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6Ik1TRTR1T3U5L1Fzc1IybzVLYVBUU2c9PSIsInZhbHVlIjoiSE1mdDVxS1pHZ1BTTkVpanUweFVrS0hjVjJkQ3lJOTRwejBXU1J0TGQ4VWdQdm44WkwxQkVxY1FTbEZIWDRWSmNNQnllVC82bkQ5dlJtUEVFcU1JRmc1dnZoNzNIbTE5V2FuWWRXYXE4SHdJbmFuZ2hLdWZlQnFiZ25lU2MxZ2QiLCJtYWMiOiIwMzlkZTc0YzQyNjZjMTkyMDIzMWExN2EyNjYxYmQ0NTRmMDcxYWY3MTNjMGQ3MDdhMTYzZTQ4ZjIyNjRlNTVlIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:47:01 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6ImlXRk5HKy8vTW1BRnNPYVlybVdJQ1E9PSIsInZhbHVlIjoiclZoYU5mdGhhUExzYXNkSzVmWGVDQ080aEZIZHp1SDNhZkdJaUQxMzVVUERFMExjWkVoSFNZQ1RsRDEycGY4emRNcm5yOHdwaHdQbE9Rc3JDZHM3U21XL29VMWpzd2VMeVo3UnFwM29KdXRvRG4vTHFxeXZzR0MyZWg1Mkk1elYiLCJtYWMiOiIyYTk3OTY4YjQxM2M1MGJmZWUxODFkNzgyZDQ1MGUzODczZTVhZTY5MGVlYjFhNGRiZTAyMGNlNmUzNWJiODM5IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:47:01 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850783250\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1308444984 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://goalconversion.gc/gc/lgn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>url</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308444984\", {\"maxDepth\":0})</script>\n"}}