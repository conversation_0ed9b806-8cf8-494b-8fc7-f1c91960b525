* {
  font-family: var(--primary-font);
}

/*
    [Main Stylesheet]

    Project:  Base Project
    Version:  1.0
    Date Created: 09/21/2016
    Date Updated: 09/21/2016
    Author: <PERSON><PERSON>
    Website: http://nghiadev.com
*/
a,
input {
  outline: none;
}

a:hover,
h1:focus,
h2:focus,
h3:focus,
h4:focus,
h5:focus {
  text-decoration: none;
}

a:focus,
input:focus {
  outline: none;
  text-decoration: none;
}

/*! normalize.css v4.1.1 | MIT License | github.com/necolas/normalize.css */
/**
 * 1. Change the default font family in all browsers (opinionated).
 * 2. Prevent adjustments of font size after orientation changes in IE and iOS.
 */
html {
  font-family: sans-serif;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/**
 * Remove the margin in all browsers (opinionated).
 */
body {
  margin: 0;
}

/* HTML5 display definitions
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 * 1. Add the correct display in Edge, IE, and Firefox.
 * 2. Add the correct display in IE.
 */
article,
aside,
footer,
header,
menu,
nav,
section {
  /* 1 */
  display: block;
}

/**
 * Add the correct display in IE 9-.
 */

/**
 * Add the correct display in iOS 4-7.
 */

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */

/**
 * Add the correct display in IE 10-.
 * 1. Add the correct display in IE.
 */

/* Links
   ========================================================================== */
/**
 * 1. Remove the gray background on active links in IE 10.
 * 2. Remove gaps in links underline in iOS 8+ and Safari 8+.
 */
a {
  background-color: transparent;
  /* 1 */
  -webkit-text-decoration-skip: objects;
  /* 2 */
}

/**
 * Remove the outline on focused links when they are also active or hovered
 * in all browsers (opinionated).
 */
a:active,
a:hover {
  outline-width: 0;
}

/* Text-level semantics
   ========================================================================== */
/**
 * 1. Remove the bottom border in Firefox 39-.
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */

/**
 * Prevent the duplicate application of `bolder` by the next rule in Safari 6.
 */

strong {
  font-weight: inherit;
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */

strong {
  font-weight: bolder;
}

/**
 * Add the correct font style in Android 4.3-.
 */

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/**
 * Add the correct background and color in IE 9-.
 */

/**
 * Add the correct font size in all browsers.
 */

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10-.
 */
img {
  border-style: none;
}

/**
 * Hide the overflow in IE.
 */

/* Grouping content
   ========================================================================== */
/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

/**
 * Add the correct margin in IE 8.
 */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */

/* Forms
   ========================================================================== */
/**
 * 1. Change font properties to `inherit` in all browsers (opinionated).
 * 2. Remove the margin in Firefox and Safari.
 */

input {
  font: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
}

/**
 * Restore the font weight unset by the previous rule.
 */

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */

input {
  /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */

/**
 * 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`
 *    controls in Android 4.
 * 2. Correct the inability to style clickable types in iOS and Safari.
 */

/**
 * Remove the inner border and padding in Firefox.
 */

/**
 * Restore the focus styles unset by the previous rule.
 */

/**
 * Change the border, margin, and padding in all browsers (opinionated).
 */

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */

/**
 * Remove the default vertical scrollbar in IE.
 */

/**
 * 1. Add the correct box sizing in IE 10-.
 * 2. Remove the padding in IE 10-.
 */

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */

/**
 * Remove the inner padding and cancel buttons in Chrome and Safari on OS X.
 */

/**
 * Correct the text style of placeholders in Chrome, Edge, and Safari.
 */
::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

img {
  max-width: 100%;
}

* {
  font-weight: 400;
}

h1, h2, h3, h4, h5 {
  position: relative;
  color: #111;
  margin-top: 0;
  margin-bottom: 10px;
}

h1 a, h2 a, h3 a, h4 a, h5 a {
  color: inherit;
}

h1 {
  font-size: 36px;
}

h2 {
  font-size: 28px;
}

h3 {
  font-size: 22px;
}

h4 {
  font-size: 18px;
}

h5 {
  font-size: 16px;
}

p {
  font-size: 14px;
  line-height: 1.5em;
  color: #999;
}

a {
  position: relative;
  color: inherit;
  text-decoration: none;
  transition: all 0.3s ease;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

p:empty {
  margin-bottom: 0;
}

.list a {
  display: inline-block;
  font-size: 13px;
  color: #666666;
}
.list li {
  position: relative;
  margin-bottom: 10px;
  transition: all 0.4s ease;
}
.list li:hover a {
  color: var(--color-1st);
}

.list--fadeIn li:before {
  content: "\f105";
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  font-family: FontAwesome;
  color: var(--color-1st);
  visibility: hidden;
  opacity: 0;
  transform: translateY(-50%);
  transition: all 0.4s ease;
}
.list--fadeIn li:hover {
  padding-left: 15px;
}
.list--fadeIn li:hover:before {
  visibility: visible;
  opacity: 1;
}

#back2top {
  position: fixed;
  bottom: 0;
  right: 40px;
  width: 40px;
  height: 40px;
  z-index: 10000;
  background-color: transparent;
  border-radius: 50%;
  border: 2px solid var(--color-1st);
  transition: all 0.5s ease;
  visibility: hidden;
  opacity: 0;
}
#back2top i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-1st);
  z-index: 10001;
  font-size: 20px;
}
#back2top:hover {
  cursor: pointer;
  background-color: var(--color-1st);
}
#back2top:hover i {
  color: #ffffff;
}
#back2top.active {
  bottom: 90px;
  visibility: visible;
  opacity: 1;
}

.navigation-toggle {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 30px;
  margin-top: 15px;
  overflow: hidden;
  float: right;
}

.navigation-toggle span, .navigation-toggle:before, .navigation-toggle:after {
  position: absolute;
  right: 5px;
  height: 2px;
  background-color: #ffffff;
  z-index: 100;
  transition: all 0.4s ease;
}

.navigation-toggle span {
  top: 50%;
  width: 15px;
  transform: translateY(-50%);
}

.navigation-toggle:before, .navigation-toggle:after {
  content: "";
}

.navigation-toggle:before {
  top: 7px;
  width: 10px;
}

.navigation-toggle:after {
  bottom: 7px;
  width: 20px;
}

.navigation-toggle:hover {
  cursor: pointer;
}

.navigation-toggle:hover span, .navigation-toggle:hover:before, .navigation-toggle:hover:after {
  width: 20px;
}

.navigation-toggle--dark span {
  background-color: #666666;
}

.navigation-toggle--dark:before, .navigation-toggle--dark:after {
  background-color: #666666;
}

.navigation-toggle--active span {
  transform: translateX(100%);
  visibility: hidden;
  opacity: 0;
}

.navigation-toggle--active:before, .navigation-toggle--active:after {
  top: 50%;
  bottom: auto;
  width: 20px;
  transform-origin: 50% 50%;
}

.navigation-toggle--active:before {
  transform: rotate(45deg);
}

.navigation-toggle--active:after {
  transform: rotate(-45deg);
}

.social {
  position: relative;
  z-index: 10;
  display: inline-block;
}

.social::after {
  clear: both;
  content: "";
  display: table;
}

.social li {
  float: left;
  margin-right: 15px;
}

.social--simple li a {
  display: inline-block;
  color: #ffffff;
  width: 25px;
  height: 25px;
  padding: 10px;
  background-color: #9d9d9d;
  border-radius: 3px;
}
.social--simple li a i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}
.social--simple li:last-child {
  margin-right: 0;
}
.social--simple li:hover a {
  background-color: var(--color-1st);
}

.page-pagination .list {
  display: inline-block;
}

.page-pagination .list::after {
  clear: both;
  content: "";
  display: table;
}

.page-pagination li {
  float: left;
  margin-right: 10px;
}

.page-pagination li.active a {
  background-color: var(--color-1st);
  color: #ffffff;
}

.page-pagination li:hover a {
  background-color: var(--color-1st);
  color: #ffffff;
}

.page-pagination a {
  min-width: 30px;
  display: inline-block;
  padding: 10px 15px;
  background-color: #ffffff;
}

.post {
  position: relative;
}

.post .post__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  transition: all 0.4s ease;
}

.post__inside .post__header {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 25px 30px;
  z-index: 20;
}

@media screen and (max-width: 991px) {
  .post__inside {
    margin-bottom: 30px;
  }
}
.post__inside--feature .post__header {
  background-color: transparent;
  background-image: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 75%);
  transition: all 0.4s ease;
}

.post__inside--feature .post__title {
  margin-bottom: 15px;
}

.post__inside--feature .post__title a {
  color: #ffffff;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.post__inside--feature .post__title:hover a {
  color: var(--color-1st);
}

.post__inside--feature .post__meta span {
  margin-right: 15px;
  color: var(--color-1st);
}

.post__inside--feature .post__meta span i {
  margin-right: 8px;
}

.post__inside--feature .post__meta .post-category i {
  margin-right: 0;
}

.post__inside--feature .post__meta span a {
  letter-spacing: 0.05em;
  font-weight: 400;
  color: #ffffff;
  font-size: 11px;
  text-transform: uppercase;
}

.post__inside--feature .post__meta span a:hover {
  color: var(--color-1st);
}

.post__inside--feature .post__meta .post-category {
  display: inline-block;
  padding: 3px 10px;
  line-height: 15px;
  background-color: var(--color-1st);
  transition: all 0.5s ease;
  color: #ffffff;
}

.post__inside--feature .post__meta .post-category a {
  display: inline-block;
  letter-spacing: 0.1em;
  color: #ffffff;
}

.post__inside--feature .post__meta .post-category a:hover {
  color: #ffffff;
}

.post__inside--feature .post__meta .post-category:hover {
  color: var(--color-1st);
  background-color: #ffffff;
}

.post__inside--feature .post__meta .post-category:hover a {
  color: var(--color-1st);
}

.post__inside--feature:hover .post__overlay {
  background-color: rgba(0, 0, 0, 0.1);
}

.post__inside--feature-small .post__header {
  padding: 15px;
}

.post__inside--feature-small .post__title {
  margin-bottom: 0;
  line-height: 1em;
}

.post__inside--feature-small .post__title a {
  text-transform: none;
  font-size: 13px;
  font-weight: 400;
}

.post__horizontal {
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.post__horizontal .post__thumbnail {
  float: left;
  width: 350px;
  position: relative;
  z-index: 20;
}

.post__horizontal .post__thumbnail .post__overlay {
  z-index: 20;
  transition: all 0.4s ease-in;
}

.post__horizontal .post__content-wrap {
  float: left;
  width: calc(100% - 350px);
  padding: 15px 20px;
}

.post__horizontal .post__content-wrap:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  background-color: #ffffff;
}

.post__horizontal .post__header, .post__horizontal .post__content, .post__horizontal .post__footer {
  position: relative;
}

.post__horizontal .post__title {
  margin-bottom: 10px;
}

.post__horizontal .post__title a {
  color: #666666;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0em;
}

.post__horizontal .post__title:hover a {
  color: var(--color-1st);
}

.post__horizontal .post__meta {
  margin-bottom: 10px;
}

.post__horizontal .post__meta span {
  margin-right: 15px;
  color: var(--color-1st);
}

.post__horizontal .post__meta span i {
  margin-right: 10px;
}

.post__horizontal .post__meta span a, .post__horizontal .post__meta .post__created-at {
  font-weight: 400;
  color: #666666;
  font-size: 12px;
  letter-spacing: 0.02em;
}

.post__horizontal .post__meta span a:hover {
  color: var(--color-1st);
}

.post__horizontal .post__content {
  margin-bottom: 15px;
}

.post__horizontal .post__content p {
  color: #666666;
  font-size: 13px;
  line-height: 1.5em;
  letter-spacing: 0.05em;
}

.post__horizontal .post__content p:after {
  content: "...";
}

@media screen and (max-width: 767px) {
  .post__horizontal .post__thumbnail, .post__horizontal .post__content-wrap {
    width: 100%;
    float: none;
  }
  .navigation {
    opacity: 0;
    display: none;
  }
}
.post__horizontal--single {
  box-shadow: none;
}

.post__horizontal--single .post__thumbnail {
  width: 100px;
}

.post__horizontal--single .post__content-wrap {
  width: calc(100% - 100px);
  padding: 0 0 0 15px;
}

.post__horizontal--single .post__content-wrap:before {
  display: none;
}

.post__horizontal--single .post__title {
  margin-bottom: 0;
  line-height: 0.8em;
}

.post__horizontal--single .post__title a {
  font-size: 14px;
  text-transform: none;
}

@media screen and (max-width: 767px) {
  .post__horizontal--single .post__thumbnail, .post__horizontal--single .post__content-wrap {
    float: left;
  }
}
.post__widget .post__thumbnail {
  float: left;
  width: 60px;
  position: relative;
  z-index: 20;
}

.post__widget .post__overlay {
  z-index: 20;
}

.post__widget .post__header {
  float: left;
  width: calc(100% - 60px);
  padding-left: 15px;
}

.post__widget .post__title {
  font-size: 13px;
  text-transform: none;
  color: #636363;
  line-height: 1.5em;
  margin-bottom: 0;
}

.post__widget .post__title a {
  font-weight: 400;
}

.post__widget .post__title:hover a {
  color: var(--color-1st);
}

.post__widget .created_at {
  color: var(--color-1st);
  font-size: 11px;
}

.post__vertical .post__thumbnail {
  position: relative;
}

.post__vertical .post__title a {
  color: #666666;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.4em;
}

.post__vertical .post__title:hover a {
  color: var(--color-1st);
}

.post__vertical--single {
  background-color: #ffffff;
}

.post__vertical--single .post__thumbnail {
  position: relative;
}

.post__vertical--single .post__content-wrap {
  position: relative;
  padding: 50px 0 25px;
}

.post__vertical--single .post__meta {
  position: absolute;
  padding-top: 3px;
  top: 0;
  left: 30px;
  width: 70px;
  height: 70px;
  z-index: 30;
  background-color: var(--color-1st);
  text-align: center;
  transform: translateY(-50%);
}

.post__vertical--single .post__meta span {
  display: inline-block;
  width: 100%;
  font-size: 10px;
  font-weight: 400;
  text-transform: uppercase;
  color: #ffffff;
  line-height: 1em;
}

.post__vertical--single .post__meta span.created__date {
  font-size: 20px;
  font-weight: 700;
}

.post__vertical--single .post__meta span.created__year {
  font-size: 14px;
  font-weight: 700;
}

.post__vertical--single .post__title a {
  font-size: 18px;
}

.post__vertical--single .post__content {
  padding-bottom: 15px;
}

.post__vertical--single .post__content p {
  font-size: 14px;
  color: #737373;
}

.post__vertical--single .post__content p:after {
  content: "...";
}

.post__vertical--single .post__readmore {
  position: relative;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 600;
  color: var(--color-1st);
  padding-bottom: 3px;
}

.post__vertical--single .post__readmore:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 50%;
  height: 1px;
  background-color: var(--color-1st);
  z-index: 20;
  transition: all 0.3s ease;
  opacity: 0;
}

.post__vertical--single .post__readmore:hover:after {
  opacity: 1;
  width: 100%;
}

.post__vertical--simple {
  background-color: transparent;
}

.post__vertical--simple .post__content-wrap {
  padding: 50px 0 15px;
}

.post__vertical--simple .post__content {
  background-color: transparent;
}

.post__vertical--simple .post__content p {
  font-size: 13px;
}
.post--single .post__header {
  border-bottom: 1px solid #eeeeee;
}

.post--single .post__title {
  font-weight: 700;
  color: #666666;
  font-size: 24px;
  letter-spacing: 0.05em;
}

.post--single .post__meta {
  margin-bottom: 10px;
}

.post--single .post__meta span {
  margin-right: 15px;
  color: var(--color-1st);
}

.post--single .post__meta span i {
  margin-right: 10px;
}

.post--single .post__meta span a {
  font-weight: 400;
  color: #666666;
  font-size: 12px;
  letter-spacing: 0.01em;
}

.post--single .post__meta span a:hover {
  color: var(--color-1st);
}

.post--single .post__meta .post__tags a {
  margin-right: 5px;
}

.post--single .post__content {
  padding: 30px 0;
}

.post--single .post__content h1, .post--single .post__content h2, .post--single .post__content h3, .post--single .post__content h4, .post--single .post__content h5, .post--single .post__content p {
  color: #666666;
}

.post--single .post__content p {
  margin-bottom: 15px;
  line-height: 1.8em;
  font-size: 13px;
}

.post--single .post__relate-group {
  text-align: left;
}

.post--single .post__relate-group.post__relate-group--right {
  text-align: right;
}

.post--single .post__relate-group.post__relate-group--right .relate__title:after {
  left: auto;
  right: 0;
}

.post--single .post__relate-group .relate__title {
  position: relative;
  margin-bottom: 20px;
  padding-bottom: 5px;
  font-size: 14px;
  font-weight: 700;
  color: #333333;
}

.post--single .post__relate-group .relate__title:after {
  content: "";
  position: absolute;
  top: 100%;
  left: 0;
  width: 25px;
  height: 2px;
  background-color: var(--color-1st);
  z-index: 10;
}

@media screen and (max-width: 767px) {
  .post--single .post__relate-group {
    margin-bottom: 30px;
  }
  .post--single .post__relate-group.post__relate-group--right {
    text-align: left;
  }
  .post--single .post__relate-group.post__relate-group--right .relate__title:after {
    left: 0;
    right: auto;
  }
}
.post--related::after {
  clear: both;
  content: "";
  display: table;
}

.post--related .post__thumbnail {
  position: relative;
  width: 100px;
  float: left;
}

.post--related .post__header {
  float: left;
  width: calc(100% - 100px);
  padding-left: 15px;
  border-bottom: none;
}

.post--related .post__title {
  font-size: 14px;
  text-transform: uppercase;
}

.post--related .post__title:hover {
  color: var(--color-1st);
}
.widget {
  margin-bottom: 30px;
  background-color: #ffffff;
  box-shadow: 0 0 7px rgba(0, 0, 0, 0.1);
}

.widget .widget__header, .widget .widget__content {
  padding: 15px;
}

.widget .widget__header {
  background-color: #f9f9f9;
}

.widget .widget__title {
  font-size: 20px;
  font-weight: 600;
  color: #363636;
  margin-bottom: 0;
}

.widget__recent-post .post {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eeeeee;
}

.widget__recent-post li:last-child .post {
  border-bottom-width: 0;
}

.widget__about p {
  font-size: 13px;
  color: #666666;
  line-height: 1.6em;
}

.widget__about .person-detail p {
  color: #e4e4e4;
  margin-bottom: 15px;
  font-size: 13px;
}

.widget__about .person-detail i {
  margin-right: 10px;
  font-size: 20px;
  vertical-align: middle;
  color: var(--color-1st);
}

.widget__about .person-detail a {
  color: #ffffff;
}

.widget__about .person-detail a:hover {
  color: var(--color-1st);
}

.widget--transparent {
  background-color: transparent;
  box-shadow: none;
}

.widget--transparent .widget__content, .widget--transparent .widget__header {
  padding: 0;
}

.widget--transparent .widget__header {
  position: relative;
  margin-bottom: 20px;
  padding-bottom: 5px;
  border: none;
  background-color: transparent;
}

.widget--transparent .widget__header:after {
  content: "";
  position: absolute;
  top: 100%;
  left: 0;
  width: 25px;
  height: 2px;
  background-color: var(--color-1st);
  z-index: 10;
}

.widget--transparent .post {
  margin-bottom: 0;
}

.widget__footer .widget__title {
  text-transform: uppercase;
  font-size: 14px;
  color: #e4e4e4;
}

.widget__tags .tag-link {
  display: inline-block;
  padding: 5px 15px;
  margin-right: 10px;
  margin-bottom: 10px;
  background-color: #ecf0f1;
  color: #666666;
  font-size: 12px;
}

.widget__tags .tag-link:last-child {
  margin-right: 0;
}

.widget__tags .tag-link:hover {
  background-color: var(--color-1st);
  color: #ffffff;
}

.widget__tags--transparent .tag-link {
  background-color: #ffffff;
}

.page-intro {
  position: relative;
  text-align: center;
}

.page-intro:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(26, 35, 126, 0.3);
  z-index: 0;
}

.page-intro .page-intro__title {
  position: relative;
  z-index: 10;
}

.page-intro .page-intro__title {
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 24px;
  margin-bottom: 20px;
}

.page-intro .social {
  margin-top: 10px;
}

.page-intro .breadcrumb {
  position: relative;
  z-index: 10;
  background-color: transparent;
  margin-bottom: 0;
}

.page-intro .breadcrumb li {
  color: #e4e4e4;
  font-size: 13px;
}

.page-intro .breadcrumb li:active {
  color: var(--color-1st);
}

.page-intro .breadcrumb a:hover {
  color: var(--color-1st);
}

.navigation .menu::after {
  clear: both;
  content: "";
  display: table;
}

.navigation .menu-item {
  float: left;
  position: relative;
}

.navigation .menu-item > a {
  display: inline-block;
  padding: 20px 15px 20px;
  line-height: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #cccccc;
}

.navigation .menu-item > .sub-menu {
  transition: all 0.4s ease;
  visibility: hidden;
  opacity: 0;
}

.navigation .menu-item.menu-item-has-children > a .toggle-icon {
  padding-left: 5px;
  font-size: 16px;
  color: #cccccc;
}

.navigation-mobile .menu-item.menu-item-has-children > a .toggle-icon {
  width: 39px;
  height: 39px;
  line-height: 39px;
  text-align: center;
  margin-top: -10px;
}

.navigation .menu-item:hover > a {
  color: #ffffff;
}

.navigation .menu-item:hover > .sub-menu {
  visibility: visible;
  opacity: 1;
}

.navigation .sub-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 100;
  min-width: 240px;
  background-color: #ffffff;
  border-top: 2px solid var(--color-1st);
  box-shadow: 0 0 1px rgba(34, 25, 25, 0.4);
}

.navigation .sub-menu > .menu-item {
  float: none;
  border-bottom: 1px dashed rgba(34, 25, 25, 0.3);
}

.navigation .sub-menu > .menu-item:last-child {
  border-bottom: 0;
}

.navigation .sub-menu > .menu-item > a {
  display: block;
  padding: 10px 20px;
  line-height: 1.4em;
  color: #666666;
  font-size: 13px;
}

.navigation--mobile {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin: 0;
  z-index: 100;
  background-color: #ffffff;
  border-top: 2px solid var(--color-1st);
  box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.3);
  visibility: hidden;
  opacity: 0;
}

.navigation--mobile .menu-item {
  float: none;
  border-bottom: 1px dashed rgba(34, 25, 25, 0.2);
}

.navigation--mobile .menu-item > a {
  position: relative;
  display: block;
  padding: 15px;
  color: #666666;
  font-size: 13px;
}

.navigation--mobile .menu-item:last-child {
  border-bottom: 0;
}

.navigation--mobile .menu-item.menu-item-has-children a .toggle-icon {
  position: absolute;
  right: 15px;
  color: #666666;
}

.navigation--mobile .menu-item > .sub-menu {
  visibility: visible;
  opacity: 1;
}

.navigation--mobile > .menu > .menu-item.menu-item--active {
  background-color: var(--color-1st);
  border-bottom: none;
}

.navigation--mobile > .menu .menu-item.menu-item--active > a {
  color: #ffffff !important;
}

.navigation--mobile > .menu .menu-item.menu-item--active > a .toggle-icon {
  color: #ffffff;
}

.navigation--mobile .menu-item:hover a {
  color: #666666;
}

.navigation--mobile .sub-menu {
  position: relative;
  border-top: none;
  display: none;
}

.navigation--mobile .sub-menu--active {
  display: block;
}

.navigation--mobile--active {
  visibility: visible;
  opacity: 1;
  display: block;
}

.navigation--light .menu-item > a {
  color: #666666;
}

.navigation--light .menu-item > a .toggle-icon {
  color: #666666;
}

.navigation--light .menu-item:hover > a, .navigation--light .menu-item.active > a {
  color: var(--color-1st);
}

.navigation--light .menu-item:hover > a .toggle-icon {
  color: var(--color-1st);
}

.page-header {
  position: relative;
  margin: 0;
  border-bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  padding: 15px 0;
  transition: all 0.4s ease-out;
}

.page-header--search-active {
  padding-top: 100px;
}

.page-header__left {
  float: left;
}

.page-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
}
.page-logo img {
  max-height: 50px;
}

.page-header__right {
  float: right;
}

.search-btn {
  float: right;
  line-height: 60px;
  padding: 0 15px;
}

.search-btn i {
  font-size: 16px;
  color: #ffffff;
}

.search-btn:hover {
  cursor: pointer;
}

.navigation {
  float: left;
  margin-right: 20px;
}

.page-header--light {
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
}

.page-header--light .search-btn i {
  color: #666666;
}

.page-footer {
  position: relative;
}

.page-footer__bottom {
  position: relative;
  padding: 20px 0 5px;
  text-align: left;
  background: #000000;
}

.page-footer__bottom .page-footer__social {
  text-align: right;
}

@media screen and (max-width: 767px) {
  .page-footer__bottom {
    text-align: center;
  }
  .page-footer__bottom .page-copyright {
    margin-bottom: 10px;
  }
  .page-footer__bottom .page-footer__social {
    text-align: center;
  }
}
.page-copyright {
  display: inline-block;
  line-height: 35px;
}

.page-copyright p {
  margin-bottom: 0;
  font-size: 13px;
  color: #666666;
  letter-spacing: 0;
  line-height: 1.5em;
}

.page-copyright p a {
  display: inline-block;
  padding-left: 5px;
  color: var(--color-1st);
}

.page-copyright p a:hover {
  color: #ffffff;
}

.post-group .post-group__header {
  position: relative;
  margin-bottom: 25px;
}

.post-group .post-group__title {
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  color: #555555;
  line-height: 1.6em;
  text-transform: uppercase;
}

.post-group .post-group__title:after {
  content: "";
  position: absolute;
  top: 100%;
  left: 0;
  margin: 0;
  width: 50px;
  height: 2px;
  background-color: var(--color-1st);
}

.post-group--hero::after {
  clear: both;
  content: "";
  display: table;
}

.post-group--hero .post-group__left, .post-group--hero .post-group__right {
  float: left;
}

.post-group--hero .post-group__left {
  padding-left: 5px;
  width: calc(50% + 5px);
}

.post-group--hero .post-group__right {
  width: calc(50% - 5px);
}

.post-group--hero .post-group__right::after {
  clear: both;
  content: "";
  display: table;
}

.post-group--hero .post {
  margin-right: 5px;
  margin-bottom: 5px;
}

.post-group--hero .post-group__item {
  width: 50%;
  float: left;
}

@media screen and (max-width: 991px) {
  .post-group--hero .post-group__left, .post-group--hero .post-group__right {
    width: 100%;
    float: none;
  }
  .post-group--hero .post-group__left {
    padding: 0;
  }
}
@media screen and (max-width: 480px) {
  .post-group--hero .post-group__item {
    width: 100%;
  }
}
.navigation--fade .menu-item:hover > .sub-menu {
  transform: translateY(0);
}

.navigation--fadeUp .menu-item > .sub-menu {
  transform: translateY(30px);
}

.navigation--fadeDown .menu-item > .sub-menu {
  transform: translateY(-30px);
}

.navigation--fadeLeft .menu-item > .sub-menu {
  transform: translateX(-30px);
}

.navigation--fadeRight .menu-item > .sub-menu {
  transform: translateX(30px);
}

.navigation--flip .menu-item > .sub-menu {
  transition: all 0.5s ease;
}

.navigation--flip .menu-item:hover > .sub-menu {
  transform: rotate(0);
}

.navigation--flipLeft .menu-item > .sub-menu {
  transform: rotate(20deg);
  transform-origin: 0 0;
}

.navigation--flipCenter .menu-item > .sub-menu {
  transform: translateX(0) rotate(-10deg);
  transform-origin: 50% 50%;
}

.navigation--flipRight .menu-item > .sub-menu {
  transform: translateX(0) rotate(-10deg);
  transform-origin: 100% 100%;
}

.navigation--mobile.navigation--fadeUp .menu-item > .sub-menu, .navigation--mobile.navigation--fadeDown .menu-item > .sub-menu, .navigation--mobile.navigation--fadeLeft .menu-item > .sub-menu, .navigation--mobile.navigation--fadeRight .menu-item > .sub-menu {
  transform: translateY(0);
}

.navigation--mobile.navigation--flip .menu-item > .sub-menu, .navigation--mobile.navigation--flipLeft .menu-item > .sub-menu, .navigation--mobile.navigation--flipCenter .menu-item > .sub-menu, .navigation--mobile.navigation--flipRight .menu-item > .sub-menu {
  transform: rotate(0);
}

.sub-menu--slideLeft .sub-menu > .menu-item {
  position: relative;
}

.sub-menu--slideLeft .sub-menu > .menu-item:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background-color: var(--color-1st);
  z-index: 0;
  transition: all 0.25s ease-out;
}

.sub-menu--slideLeft .sub-menu > .menu-item > a {
  position: relative;
  z-index: 10;
  transition: all 0.25s ease;
}

.sub-menu--slideLeft .sub-menu > .menu-item > a:before {
  content: "\f105";
  margin-right: 5px;
  font-family: FontAwesome;
  font-size: 13px;
  color: #ffffff;
  visibility: hidden;
  transition: all 0.25s ease;
}

.sub-menu--slideLeft .sub-menu > .menu-item:hover:after {
  width: 100%;
}

.sub-menu--slideLeft .sub-menu > .menu-item:hover > a {
  padding-left: 30px;
  color: #ffffff;
}

.sub-menu--slideLeft .sub-menu > .menu-item:hover > a:before {
  visibility: visible;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-40 {
  margin-bottom: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-100 {
  padding-bottom: 100px;
}

.bg-cover {
  background-position: 50% 50%;
  background-size: cover;
}

.bg-lightgray {
  background-color: #ecf0f1;
}

.bg-dark {
  background-color: #212121;
}

.bg-overlay {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0.9;
  background-color: #000000;
}

body[dir=rtl] .widget--transparent .widget__header:after {
  left: auto;
  right: 0;
}
body[dir=rtl] .post-group .post-group__title:after {
  left: auto;
  right: 0;
}
body[dir=rtl] .widget__about .person-detail i {
  margin-right: 0;
  margin-left: 10px;
}
body[dir=rtl] .header .header-wrap .nav-top ul li a i {
  margin-right: 0;
  margin-left: 5px;
}
body[dir=rtl] .language-wrapper .language_bar_list li a img {
  margin-right: 0;
  margin-left: 8px;
}
.super-search.active {
  z-index: 88888888888;
  width: 100%;
  height: 100%;
  overflow: hidden;
  top: 0;
  right: 0;
  border: none;
  overflow-y: scroll;
  position: fixed;
  background: #ffffff;
  display: block !important;
}

.super-search.active .quick-search {
  width: 90%;
  margin: 40px auto;
}

.super-search.active .search-input {
  color: var(--color-1st) !important;
  font-size: 60px;
}

@media screen and (max-width: 991px) {
  .super-search.active .search-input {
    font-size: 30px;
  }
}
.c-layout-header .super-search.active .quick-search > span {
  font-size: 60px;
}

.search-result {
  display: none;
}

.super-search.active .search-result {
  width: 90%;
  margin: 20px auto;
}
.super-search .search-list .row > div {
  display: none;
  opacity: 0;
  transform: translateY(100%);
  animation-duration: 0.5s;
  animation-fill-mode: both;
  animation-name: bounce-in;
  animation-timing-function: cubic-bezier(0.32, 1.25, 0.375, 1.15);
}

.search-finished .search-list .row > div {
  display: block;
  opacity: 1;
}

.super-search .search-list .row > div:nth-child(1) {
  animation-delay: 0.15s;
}

.search-list {
  margin: -65px 0 130px;
  padding: 0;
  list-style: none;
}

.search-list a {
  display: block;
  position: relative;
  background: #f1f2f1;
  color: #a1a1a1;
  font-size: 16px;
  line-height: 22px;
  border-radius: 2px;
  margin-bottom: 10px;
  padding: 25px;
  overflow: hidden;
  text-decoration: none !important;
  transition: all 225ms ease;
  height: 80px;
}

.search-list .has-image {
  padding-left: 115px;
}

.search-list .row > div a.squared .img {
  border-radius: 0;
  height: 100%;
  width: 100px;
  left: 0;
  transition: transform 450ms cubic-bezier(0.32, 1.25, 0.375, 1.15);
  display: block;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-size: cover;
  background: #545454 50% 0 no-repeat;
  text-align: center;
  line-height: 100px;
}

.search-list .row > div a.squared .img .fa, .search-list .row > div a.squared .img .glyphicon {
  font-size: 34px !important;
}

.search-list .row > div a .spoofer {
  opacity: 0;
  display: block;
}

.search-list .row > div a.squared .visible {
  padding: 25px 10px 25px 115px;
  transition: transform 450ms cubic-bezier(0.32, 1.25, 0.375, 1.15);
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  transform: translateY(-50%);
  display: block;
}

.search-list .row > div a:focus, .search-list .row > div a:hover {
  color: #ffffff;
  background: #f38b72;
}

.search-list .row > div a.squared:focus .visible, .search-list .row > div a.squared:hover .visible {
  transform: translateX(-80px) translateY(-50%);
}

.search-list .row > div a.squared:focus .img, .search-list .row > div a.squared:hover .img {
  transform: translateX(-100%) translateY(-50%);
}

.super-search .group:nth-child(1) h4 {
  animation-delay: 0.2s;
}

.super-search .search-list h4, .super-search .search-list .row > div {
  display: none;
  opacity: 0;
  transform: translateY(100%);
  animation-duration: 0.5s;
  animation-fill-mode: both;
  animation-name: bounce-in;
  animation-timing-function: cubic-bezier(0.32, 1.25, 0.375, 1.15);
  margin-top: 0;
  margin-bottom: 5px;
}

.super-search .search-list h4 {
  color: rgba(0, 0, 0, 0.5);
}

.super-search .search-list h4, .super-search .search-list .row > div {
  -webkit-animation-name: bounce-in;
  -moz-animation-name: bounce-in;
  -webkit-animation-timing-function: cubic-bezier(0.32, 1.25, 0.375, 1.15);
  -moz-animation-timing-function: cubic-bezier(0.32, 1.25, 0.375, 1.15);
}

.search-list h4 {
  opacity: 0.7;
  font-size: 20px;
  letter-spacing: 0.01em;
}

.search-finished .search-list h4, .search-finished .search-list .row > div {
  display: block;
  opacity: 1;
}
.search-result-title {
  padding: 10px 0;
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 700;
  color: #1d9977;
}

.close-search.active {
  display: inline-block;
  position: absolute;
  top: 50px;
  right: 30px;
  color: #828b96;
  font-size: 60px;
  cursor: pointer;
}

.quick-search > .form-control {
  display: block;
  font-weight: 400;
  border: 0;
  background: transparent;
  box-shadow: none;
  border-radius: 0;
  padding: 10px 0;
  height: 100px;
  color: #32c5d2 !important;
  font-size: 60px;
}

.social--widget a span {
  display: none;
}

#list-photo {
  width: 100%;
}

#list-photo .item {
  width: 33.333%;
  margin-bottom: 14px;
}

#list-photo .item .photo-item {
  padding-left: 7px;
  padding-right: 7px;
}

#list-photo .item .photo-item div {
  transition: all 0.25s;
  padding: 5px;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.3);
}

#list-photo .item .photo-item div:hover {
  background: rgba(63, 63, 62, 0.1);
}

#list-photo .item .photo-item img {
  border: 1px solid rgba(63, 63, 62, 0.4);
  display: block;
}

.gallery-wrap .gallery-item {
  width: 32.8%;
  margin-right: 0.8%;
  float: left;
  max-height: 250px;
  overflow: hidden;
  margin-bottom: 10px;
  position: relative;
}

.gallery-wrap .gallery-item:nth-child(3n) {
  margin-right: 0;
}

.gallery-wrap .gallery-item .gallery-detail {
  position: absolute;
  bottom: -50px;
  right: 0;
  left: 0;
  z-index: 2;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 4px 10px;
  transition: ease 0.25s;
}

.gallery-wrap .gallery-item .gallery-detail a {
  color: #ffffff;
}

.gallery-wrap .gallery-item .gallery-detail a:hover {
  color: #32c5d2 !important;
}

.gallery-wrap .gallery-item:hover .gallery-detail {
  bottom: 0;
}

.gallery-wrap .gallery-item .gallery-detail .gallery-title {
  text-transform: uppercase;
  font-weight: bold;
}

.gallery-wrap .gallery-item .img-wrap {
  overflow: hidden;
}

.gallery-wrap .gallery-item .img-wrap img {
  width: 100%;
}

.fb-like {
  margin: 15px 0;
}

.page-pagination ul {
  margin: 0;
}

.page-pagination li {
  margin: 0 0 0 10px;
}

.page-pagination li a, .page-pagination li span {
  border: none;
  outline: none;
  border-radius: 0 !important;
  color: var(--color-1st);
}

.page-pagination li:hover a, .page-pagination li:hover span, .page-pagination li.active span {
  background-color: var(--color-1st) !important;
  color: #ffffff !important;
}

.language-wrapper {
  height: 40px;
  line-height: 30px;
}
.language-wrapper .language_bar_list li {
  display: inline-block;
  margin-left: 5px;
}
.language-wrapper .language_bar_list li a {
  text-align: left;
}
.language-wrapper .language_bar_list li a img {
  float: left;
  margin-right: 8px;
  margin-top: -1px;
  width: 20px;
}

@media (max-width: 767px) {
  .header .header-wrap {
    display: none;
  }
}
.header .header-wrap .nav-top ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.header .header-wrap .nav-top ul:after, .header .header-wrap .nav-top ul:before {
  content: " ";
  display: table;
}
.header .header-wrap .nav-top ul:after {
  clear: both;
}
.header .header-wrap .nav-top ul li {
  float: left;
}
.header .header-wrap .nav-top ul li a {
  display: inline-block;
  line-height: 42px;
  font-size: 13px;
  margin-right: 25px;
}
.header .header-wrap .nav-top ul li a:hover {
  color: var(--color-1st);
}
.header .header-wrap .nav-top .hi-icon-wrap {
  margin-top: 10px;
}
.header .header-wrap .nav-top .hi-icon-wrap a {
  margin-left: 5px;
  color: #ffffff;
  background-color: #9d9d9d;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  line-height: 26px;
  text-align: center;
}
.header .header-wrap .nav-top .hi-icon-wrap a:first-child {
  margin-left: 0;
}
.header .header-wrap .nav-top .hi-icon-wrap a:hover {
  background-color: var(--color-1st);
}
.header .header-wrap .logo {
  margin: 0;
  padding: 0;
  float: left;
  position: relative;
  height: 90px;
}
.header .header-wrap .logo img {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.hi-icon-effect-3a .hi-icon {
  color: #4089ea;
}

.hi-icon-effect-3a .hi-icon:hover {
  color: #666666;
}

.hi-icon-effect-3a .hi-icon:hover:after {
  opacity: 0;
  filter: alpha(opacity=0);
  transform: scale(1.3);
}

.header-wrap {
  background-color: #ffffff;
  color: #ffffff;
}

.nav-top {
  background-color: #f5f5f5;
  color: #666666;
}
.widget__tags .tag-link {
  background-color: var(--color-1st);
  color: #ffffff;
}
figure.image {
  margin: 10px 0;
  display: inline-block;
}
figure.image figcaption {
  background: #f5f5f5;
  padding: 10px;
  font-size: 90%;
  text-align: center;
}
 .post__content ul {
  padding-left: 25px;
  list-style: disc;
}
 .post__content ul li {
  font-weight: 400;
  list-style: disc;
}

.navigation:not(.navigation--mobile) .sub-menu .sub-menu {
  left: 100%;
  top: 0;
}
.navigation.navigation--mobile .menu-item.menu-item--active > a .toggle-icon {
  color: #fff !important;
}
.navigation.navigation--mobile .sub-menu--slideLeft .sub-menu > .menu-item.menu-item--active > a {
  color: var(--color-1st) !important;
  background: #ffffff;
}
.navigation.navigation--mobile .sub-menu--slideLeft .sub-menu > .menu-item.menu-item--active > a .toggle-icon, .navigation.navigation--mobile .sub-menu--slideLeft .sub-menu > .menu-item.menu-item--active > a:before {
  color: var(--color-1st) !important;
}
.navigation.navigation--mobile .sub-menu--slideLeft .sub-menu .sub-menu > .menu-item > a {
  margin-left: 20px;
}

.header .header-wrap .nav-top ul li a {
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .header-wrap .nav-top ul li a i {
  margin-right: 5px;
}

.page-content .gallery-wrap .gallery-item .gallery-detail a:hover {
  color: var(--color-1st) !important;
}

.post__widget .post__created-at {
  font-weight: 400;
  color: #666666;
  font-size: 12px;
  letter-spacing: 0.02em;
}

.hide {
  display: none !important;
}

.img-circle {
  border-radius: 50%;
}

body {
  font-size: 14px;
  line-height: 1.42857143;
}

.post__content .page-pagination ul {
  padding-left: 0;
  list-style: none;
}
.post__content .page-pagination ul li {
  list-style: none;
}
.post__content .page-pagination ul li span {
  min-width: 30px;
  display: inline-block;
  padding: 10px 15px;
}

@media (min-width: 1400px) {
   .container {
    width: 1170px;
  }
}

/*# sourceMappingURL=style.css.map*/