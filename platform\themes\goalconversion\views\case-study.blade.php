<!-- Case Study Hero Section -->
<div class="case-study-hero" style="background-image: url({{ $caseStudy->hero_image ?? Theme::asset()->url('img/bg/inner-hero-bg.jpg') }});">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 m-auto text-center">
                <div class="case-study-hero-content">
                    @if($caseStudy->category)
                    <div class="case-study-category">
                        <span class="category-badge">{{ $caseStudy->category }}</span>
                    </div>
                    @endif
                    <h1 class="case-study-title text-white">{{ $caseStudy->title ?? 'Case Study Title' }}</h1>
                    @if($caseStudy->subtitle)
                    <p class="case-study-subtitle text-white">{{ $caseStudy->subtitle }}</p>
                    @endif
                    <div class="case-study-meta">
                        @if($caseStudy->client_name)
                        <div class="meta-item">
                            <span class="meta-label">Client:</span>
                            <span class="meta-value">{{ $caseStudy->client_name }}</span>
                        </div>
                        @endif
                        @if($caseStudy->duration)
                        <div class="meta-item">
                            <span class="meta-label">Duration:</span>
                            <span class="meta-value">{{ $caseStudy->duration }}</span>
                        </div>
                        @endif
                        @if($caseStudy->industry)
                        <div class="meta-item">
                            <span class="meta-label">Industry:</span>
                            <span class="meta-value">{{ $caseStudy->industry }}</span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Case Study Content -->
<div class="case-study-content section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Project Overview -->
                @if($caseStudy->overview)
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Project Overview</h2>
                    </div>
                    <div class="section-content">
                        <div class="ck-content">
                            {!! BaseHelper::clean($caseStudy->overview) !!}
                        </div>
                    </div>
                </div>
                @endif

                <!-- Challenge Section -->
                @if($caseStudy->challenges || $caseStudy->challenge_description)
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>The Challenge</h2>
                    </div>
                    <div class="section-content">
                        @if($caseStudy->challenge_description)
                        <div class="ck-content mb-4">
                            {!! BaseHelper::clean($caseStudy->challenge_description) !!}
                        </div>
                        @endif

                        @if($caseStudy->challenges && is_array($caseStudy->challenges))
                        <div class="challenge-grid">
                            @foreach($caseStudy->challenges as $challenge)
                            <div class="challenge-item">
                                <div class="challenge-icon">
                                    <i class="{{ $challenge['icon'] ?? 'fas fa-exclamation-triangle' }}"></i>
                                </div>
                                <div class="challenge-content">
                                    <h4>{{ $challenge['title'] ?? 'Challenge' }}</h4>
                                    <p>{{ $challenge['description'] ?? '' }}</p>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Solution Section -->
                @if($caseStudy->solutions || $caseStudy->solution_description)
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Our Solution</h2>
                    </div>
                    <div class="section-content">
                        @if($caseStudy->solution_description)
                        <div class="ck-content mb-4">
                            {!! BaseHelper::clean($caseStudy->solution_description) !!}
                        </div>
                        @endif

                        @if($caseStudy->solutions && is_array($caseStudy->solutions))
                        <div class="solution-timeline">
                            @foreach($caseStudy->solutions as $index => $solution)
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <span class="timeline-number">{{ $index + 1 }}</span>
                                </div>
                                <div class="timeline-content">
                                    <h4>{{ $solution['title'] ?? 'Solution Step' }}</h4>
                                    @if($solution['description'])
                                    <p>{{ $solution['description'] }}</p>
                                    @endif
                                    @if($solution['features'] && is_array($solution['features']))
                                    <ul class="solution-list">
                                        @foreach($solution['features'] as $feature)
                                        <li>{{ $feature }}</li>
                                        @endforeach
                                    </ul>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Results Section -->
                @if($caseStudy->results || $caseStudy->result_description)
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Results & Impact</h2>
                    </div>
                    <div class="section-content">
                        @if($caseStudy->result_description)
                        <div class="ck-content mb-4">
                            {!! BaseHelper::clean($caseStudy->result_description) !!}
                        </div>
                        @endif

                        @if($caseStudy->results && is_array($caseStudy->results))
                        <div class="results-grid">
                            @foreach($caseStudy->results as $result)
                            <div class="result-card">
                                <div class="result-number">{{ $result['number'] ?? '0' }}</div>
                                <div class="result-label">{{ $result['label'] ?? 'Result' }}</div>
                                @if($result['description'])
                                <div class="result-description">{{ $result['description'] }}</div>
                                @endif
                            </div>
                            @endforeach
                        </div>
                        @endif

                        @if($caseStudy->chart_image)
                        <div class="results-chart-section">
                            @if($caseStudy->chart_title)
                            <h4>{{ $caseStudy->chart_title }}</h4>
                            @endif
                            <div class="chart-placeholder">
                                <img src="{{ RvMedia::getImageUrl($caseStudy->chart_image) }}" alt="{{ $caseStudy->chart_title ?? 'Results Chart' }}" class="img-fluid">
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Process & Tools -->
                @if($caseStudy->tools || $caseStudy->process_description)
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Process & Tools</h2>
                    </div>
                    <div class="section-content">
                        @if($caseStudy->process_description)
                        <div class="ck-content mb-4">
                            {!! BaseHelper::clean($caseStudy->process_description) !!}
                        </div>
                        @endif

                        @if($caseStudy->tools && is_array($caseStudy->tools))
                        <div class="tools-grid">
                            @foreach($caseStudy->tools as $toolCategory)
                            <div class="tool-category">
                                <h4>{{ $toolCategory['category'] ?? 'Tools' }}</h4>
                                @if($toolCategory['tools'] && is_array($toolCategory['tools']))
                                <div class="tool-list">
                                    @foreach($toolCategory['tools'] as $tool)
                                    <span class="tool-item">{{ $tool }}</span>
                                    @endforeach
                                </div>
                                @endif
                            </div>
                            @endforeach
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Gallery Section -->
                @if($caseStudy->gallery && !empty($caseStudy->gallery))
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Project Gallery</h2>
                    </div>
                    <div class="section-content">
                        <div class="gallery-grid">
                            @foreach($caseStudy->gallery as $image)
                            @if($image)
                            <div class="gallery-item">
                                <img src="{{ RvMedia::getImageUrl($image) }}" alt="{{ $caseStudy->title }}" class="img-fluid">
                                @if($image['caption'] ?? false)
                                <div class="gallery-caption">{{ $image['caption'] }}</div>
                                @endif
                            </div>
                            @endif
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif

                <!-- Testimonial -->
                @if($caseStudy->testimonial_content || $caseStudy->client_testimonial)
                <div class="case-study-section">
                    <div class="case-study-testimonial">
                        <div class="testimonial-content">
                            <div class="testimonial-quote">
                                <i class="fas fa-quote-left"></i>
                            </div>
                            <blockquote>
                                "{{ $caseStudy->testimonial_content ?? $caseStudy->client_testimonial ?? 'Great results achieved through professional service.' }}"
                            </blockquote>
                            <div class="testimonial-author">
                                @if($caseStudy->client_avatar)
                                <div class="author-image">
                                    <img src="{{ RvMedia::getImageUrl($caseStudy->client_avatar) }}" alt="{{ $caseStudy->client_name ?? 'Client' }}">
                                </div>
                                @endif
                                <div class="author-info">
                                    <h5>{{ $caseStudy->client_name ?? 'Client Name' }}</h5>
                                    @if($caseStudy->client_position)
                                    <span>{{ $caseStudy->client_position }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="case-study-sidebar">
                    <!-- Project Details -->
                    <div class="sidebar-widget">
                        <h3 class="widget-title">Project Details</h3>
                        <div class="project-details">
                            @if($caseStudy->client_name)
                            <div class="detail-item">
                                <span class="detail-label">Client:</span>
                                <span class="detail-value">{{ $caseStudy->client_name }}</span>
                            </div>
                            @endif
                            @if($caseStudy->industry)
                            <div class="detail-item">
                                <span class="detail-label">Industry:</span>
                                <span class="detail-value">{{ $caseStudy->industry }}</span>
                            </div>
                            @endif
                            @if($caseStudy->duration)
                            <div class="detail-item">
                                <span class="detail-label">Project Duration:</span>
                                <span class="detail-value">{{ $caseStudy->duration }}</span>
                            </div>
                            @endif
                            @if($caseStudy->team_size)
                            <div class="detail-item">
                                <span class="detail-label">Team Size:</span>
                                <span class="detail-value">{{ $caseStudy->team_size }}</span>
                            </div>
                            @endif
                            @if($caseStudy->budget_range)
                            <div class="detail-item">
                                <span class="detail-label">Budget Range:</span>
                                <span class="detail-value">{{ $caseStudy->budget_range }}</span>
                            </div>
                            @endif
                            @if($caseStudy->services)
                            <div class="detail-item">
                                <span class="detail-label">Services:</span>
                                <span class="detail-value">{{ $caseStudy->services }}</span>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Key Metrics -->
                    @if($caseStudy->key_metrics && is_array($caseStudy->key_metrics))
                    <div class="sidebar-widget">
                        <h3 class="widget-title">Key Metrics</h3>
                        <div class="metrics-list">
                            @foreach($caseStudy->key_metrics as $metric)
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="{{ $metric['icon'] ?? 'fas fa-chart-line' }}"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-number">{{ $metric['number'] ?? '0' }}</div>
                                    <div class="metric-label">{{ $metric['label'] ?? 'Metric' }}</div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Related Services -->
                    <div class="sidebar-widget">
                        <h3 class="widget-title">Related Services</h3>
                        <div class="related-services">
                            <a href="#" class="service-link">
                                <i class="fas fa-search"></i>
                                <span>Search Engine Optimization</span>
                            </a>
                            <a href="#" class="service-link">
                                <i class="fas fa-ad"></i>
                                <span>Pay-Per-Click Advertising</span>
                            </a>
                            <a href="#" class="service-link">
                                <i class="fas fa-chart-bar"></i>
                                <span>Conversion Rate Optimization</span>
                            </a>
                            <a href="#" class="service-link">
                                <i class="fas fa-pen"></i>
                                <span>Content Marketing</span>
                            </a>
                            <a href="#" class="service-link">
                                <i class="fas fa-share-alt"></i>
                                <span>Social Media Marketing</span>
                            </a>
                        </div>
                    </div>

                    <!-- Contact CTA -->
                    <div class="sidebar-widget">
                        <div class="cta-widget">
                            <h3>Ready to Grow Your Business?</h3>
                            <p>Get similar results for your company. Let's discuss your project and create a custom strategy.</p>
                            <a href="#" class="theme-btn1 w-100 text-center">Get Free Consultation</a>
                            <div class="cta-contact">
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>+****************</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="case-study-cta section-padding" style="background-color: var(--gc-bg-common-3);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 m-auto text-center">
                <div class="cta-content">
                    <h2 class="cta-title">Ready to Achieve Similar Results?</h2>
                    <p class="cta-description">Join hundreds of successful businesses that have transformed their digital presence with our proven strategies. Let's discuss how we can help you achieve your growth goals.</p>
                    <div class="cta-buttons">
                        <a href="#" class="theme-btn1">Start Your Project</a>
                        <a href="#" class="theme-btn4">View More Case Studies</a>
                    </div>
                    <div class="cta-stats">
                        <div class="stat-item">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Successful Projects</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">98%</div>
                            <div class="stat-label">Client Satisfaction</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">5 Years</div>
                            <div class="stat-label">Average Partnership</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
