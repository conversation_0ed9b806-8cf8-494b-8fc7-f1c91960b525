<?php

namespace Shaqi\CaseStudies\Repositories\Eloquent;

use Shaqi\Support\Repositories\Eloquent\RepositoriesAbstract;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyCategoryInterface;
use Illuminate\Support\Collection;

class CaseStudyCategoryRepository extends RepositoriesAbstract implements CaseStudyCategoryInterface
{
    public function getDataSiteMap(): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->with('slugable')
            ->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getFeaturedCategories(int $limit = 5, array $with = ['slugable']): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->where('is_featured', true)
            ->with($with)
            ->orderBy('order')
            ->orderByDesc('created_at')
            ->limit($limit);

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getAllCategories(array $condition = [], array $with = []): Collection
    {
        $data = $this->model->with($with);

        if (! empty($condition)) {
            $data = $data->where($condition);
        } else {
            $data = $data->wherePublished();
        }

        $data = $data->orderBy('order')->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getAllCategoriesWithChildren(array $condition = [], array $with = [], array $select = ['*']): Collection
    {
        $data = $this->model
            ->with(array_merge(['children'], $with))
            ->select($select);

        if (! empty($condition)) {
            $data = $data->where($condition);
        } else {
            $data = $data->wherePublished();
        }

        $data = $data->orderBy('order')->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getPopularCategories(int $limit = 10, array $with = ['slugable'], array $withCount = ['caseStudies']): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->with($with)
            ->withCount($withCount)
            ->orderByDesc('case_studies_count')
            ->orderBy('order')
            ->orderByDesc('created_at')
            ->limit($limit);

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getCategories(array $select, array $orderBy, array $condition = []): Collection
    {
        $data = $this->model->select($select);

        if (! empty($condition)) {
            $data = $data->where($condition);
        }

        foreach ($orderBy as $column => $direction) {
            $data = $data->orderBy($column, $direction);
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }
}
