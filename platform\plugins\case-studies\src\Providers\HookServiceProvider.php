<?php

namespace Shaqi\CaseStudies\Providers;

use <PERSON>haqi\Base\Facades\Html;
use Shaqi\Menu\Facades\Menu;
use <PERSON>haqi\Base\Facades\Assets;
use Shaqi\Media\Facades\RvMedia;
use <PERSON><PERSON>qi\Base\Facades\BaseHelper;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Shaqi\CaseStudies\Models\CaseStudy;
use Shaqi\Slug\Events\UpdatedSlugEvent;
use Shaqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Supports\ServiceProvider;
use Shaqi\Menu\Events\RenderingMenuOptions;
use Illuminate\Database\Eloquent\Collection;
use Shaqi\CaseStudies\Models\CaseStudyCategory;
use Shaqi\CaseStudies\Services\CaseStudyService;
use Shaqi\Base\Forms\FieldOptions\SelectFieldOption;
use Shaqi\Dashboard\Events\RenderingDashboardWidgets;
use <PERSON><PERSON>qi\Dashboard\Supports\DashboardWidgetInstance;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Menu::addMenuOptionModel(CaseStudyCategory::class);
        Menu::addMenuOptionModel(CaseStudy::class);

        $this->app['events']->listen(RenderingMenuOptions::class, function (): void {
            add_action(MENU_ACTION_SIDEBAR_OPTIONS, [$this, 'registerMenuOptions'], 2);
        });

        $this->app['events']->listen(RenderingDashboardWidgets::class, function (): void {
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [$this, 'registerDashboardWidgets'], 21, 2);
        });

        add_filter(BASE_FILTER_PUBLIC_SINGLE_DATA, [$this, 'handleSingleView'], 2);

        Event::listen(UpdatedSlugEvent::class, [$this, 'handleUpdatedSlugEvent']);
    }



    public function registerMenuOptions(): void
    {
        if (Auth::guard()->user()->hasPermission('case-studies.index')) {
            Menu::registerMenuOptions(CaseStudy::class, trans('plugins/case-studies::case-studies.menu'));
        }
    }

    public function registerDashboardWidgets(array $widgets, Collection $widgetSettings): array
    {
        if (! Auth::guard()->user()->hasPermission('case-studies.index')) {
            return $widgets;
        }

        Assets::addScriptsDirectly(['/vendor/core/plugins/case-studies/js/case-studies.js']);

        return (new DashboardWidgetInstance())
            ->setPermission('case-studies.index')
            ->setKey('widget_case_studies_recent')
            ->setTitle(trans('plugins/case-studies::case-studies.widget_case_studies_recent'))
            ->setIcon('fas fa-briefcase')
            ->setColor('yellow')
            ->setRoute(route('case-studies.widget.recent-case-studies'))
            ->setBodyClass('')
            ->setColumn('col-md-6 col-sm-6')
            ->init($widgets, $widgetSettings);
    }

    public function handleSingleView($slug)
    {
        return (new CaseStudyService())->handleFrontRoutes($slug);
    }
}
