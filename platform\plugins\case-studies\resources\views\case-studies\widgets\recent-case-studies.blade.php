<div class="widget meta-boxes">
    <div class="widget-title">
        <h4>{{ trans('Recent Case Studies') }}</h4>
    </div>
    <div class="widget-body">
        @if($caseStudies->isNotEmpty())
            <div class="list-group">
                @foreach($caseStudies as $caseStudy)
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <a href="{{ route('case-studies.edit', $caseStudy->id) }}">
                                    {{ $caseStudy->name }}
                                </a>
                            </h6>
                            <small>{{ $caseStudy->created_at->diffFor<PERSON>umans() }}</small>
                        </div>
                        @if($caseStudy->description)
                            <p class="mb-1">{{ Str::limit($caseStudy->description, 100) }}</p>
                        @endif
                        @if($caseStudy->client_name)
                            <small>Client: {{ $caseStudy->client_name }}</small>
                        @endif
                    </div>
                @endforeach
            </div>
        @else
            <p class="text-muted">{{ trans('No case studies found.') }}</p>
        @endif
    </div>
</div>
