<?php

namespace Shaqi\CaseStudies\Repositories\Eloquent;

use <PERSON>haqi\Support\Repositories\Eloquent\RepositoriesAbstract;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyInterface;
use <PERSON><PERSON>qi\CaseStudies\Models\CaseStudy;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class CaseStudyRepository extends RepositoriesAbstract implements CaseStudyInterface
{
    public function getFeatured(int $limit = 5, array $with = []): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->where('is_featured', true)
            ->limit($limit)
            ->with(array_merge(['slugable'], $with))
            ->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getListCaseStudyNonInList(array $selected = [], int $limit = 7, array $with = []): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->whereNotIn('id', $selected)
            ->limit($limit)
            ->with($with)
            ->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getRelated(int|string $id, int $limit = 3): Collection
    {
        $caseStudy = $this->findById($id);

        if (! $caseStudy) {
            return collect();
        }

        $categoryIds = $this->getRelatedCategoryIds($caseStudy);

        if (empty($categoryIds)) {
            return $this->getListCaseStudyNonInList([$id], $limit);
        }

        $data = $this->model
            ->wherePublished()
            ->where('id', '!=', $id)
            ->whereHas('categories', function ($query) use ($categoryIds) {
                $query->whereIn('case_study_categories.id', $categoryIds);
            })
            ->limit($limit)
            ->with(['slugable', 'categories'])
            ->orderByDesc('created_at');

        $related = $this->applyBeforeExecuteQuery($data)->get();

        if ($related->count() < $limit) {
            $remaining = $limit - $related->count();
            $additional = $this->getListCaseStudyNonInList(
                array_merge([$id], $related->pluck('id')->toArray()),
                $remaining
            );
            $related = $related->merge($additional);
        }

        return $related;
    }

    public function getRelatedCategoryIds(CaseStudy|int|string $model): array
    {
        if (! $model instanceof CaseStudy) {
            $model = $this->findById($model);
        }

        if (! $model) {
            return [];
        }

        return $model->categories()->pluck('case_study_categories.id')->toArray();
    }

    public function getByCategory(array|int|string $categoryId, int $paginate = 12, int $limit = 0): Collection|LengthAwarePaginator
    {
        if (! is_array($categoryId)) {
            $categoryId = [$categoryId];
        }

        $data = $this->model
            ->wherePublished()
            ->whereHas('categories', function ($query) use ($categoryId) {
                $query->whereIn('case_study_categories.id', $categoryId);
            })
            ->with(['slugable', 'categories'])
            ->orderByDesc('created_at');

        if ($limit) {
            return $this->applyBeforeExecuteQuery($data)->limit($limit)->get();
        }

        return $this->applyBeforeExecuteQuery($data)->paginate($paginate);
    }

    public function getByUserId(int|string $authorId, int $paginate = 6): Collection|LengthAwarePaginator
    {
        $data = $this->model
            ->wherePublished()
            ->where('author_id', $authorId)
            ->with(['slugable', 'categories'])
            ->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->paginate($paginate);
    }

    public function getDataSiteMap(): Collection|LengthAwarePaginator
    {
        $data = $this->model
            ->wherePublished()
            ->with('slugable')
            ->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getRecentCaseStudies(int $limit = 5, int|string $categoryId = 0): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->with(['slugable', 'categories'])
            ->orderByDesc('created_at')
            ->limit($limit);

        if ($categoryId) {
            $data = $data->whereHas('categories', function ($query) use ($categoryId) {
                $query->where('case_study_categories.id', $categoryId);
            });
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getSearch(?string $keyword, int $limit = 10, int $paginate = 10): Collection|LengthAwarePaginator
    {
        $data = $this->model
            ->wherePublished()
            ->with(['slugable', 'categories']);

        if ($keyword) {
            $data = $data->where(function ($query) use ($keyword) {
                $query->where('name', 'LIKE', '%' . $keyword . '%')
                    ->orWhere('description', 'LIKE', '%' . $keyword . '%')
                    ->orWhere('content', 'LIKE', '%' . $keyword . '%');
            });
        }

        $data = $data->orderByDesc('created_at');

        if ($limit) {
            return $this->applyBeforeExecuteQuery($data)->limit($limit)->get();
        }

        return $this->applyBeforeExecuteQuery($data)->paginate($paginate);
    }

    public function getAllCaseStudies(int $perPage = 12, bool $active = true, array $with = ['slugable']): Collection|LengthAwarePaginator
    {
        $data = $this->model->with($with);

        if ($active) {
            $data = $data->wherePublished();
        }

        $data = $data->orderByDesc('created_at');

        if ($perPage > 0) {
            return $this->applyBeforeExecuteQuery($data)->paginate($perPage);
        }

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getPopularCaseStudies(int $limit, array $args = []): Collection
    {
        $data = $this->model
            ->wherePublished()
            ->with(['slugable', 'categories'])
            ->orderByDesc('views')
            ->orderByDesc('created_at')
            ->limit($limit);

        return $this->applyBeforeExecuteQuery($data)->get();
    }

    public function getFilters(array $filters): Collection|LengthAwarePaginator
    {
        $data = $this->model
            ->wherePublished()
            ->with(['slugable', 'categories']);

        if (! empty($filters['keyword'])) {
            $data = $data->where(function ($query) use ($filters) {
                $query->where('name', 'LIKE', '%' . $filters['keyword'] . '%')
                    ->orWhere('description', 'LIKE', '%' . $filters['keyword'] . '%');
            });
        }

        if (! empty($filters['category_id'])) {
            $data = $data->whereHas('categories', function ($query) use ($filters) {
                $query->where('case_study_categories.id', $filters['category_id']);
            });
        }

        $data = $data->orderByDesc('created_at');

        return $this->applyBeforeExecuteQuery($data)->paginate($filters['per_page'] ?? 12);
    }
}
