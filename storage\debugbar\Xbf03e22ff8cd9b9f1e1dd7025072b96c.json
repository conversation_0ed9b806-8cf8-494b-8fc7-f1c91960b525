{"__meta": {"id": "Xbf03e22ff8cd9b9f1e1dd7025072b96c", "datetime": "2025-07-18 23:04:43", "utime": **********.152097, "method": "GET", "uri": "/gc/lgn/menu-items-count", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752879881.845547, "end": **********.152123, "duration": 1.3065760135650635, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1752879881.845547, "relative_start": 0, "end": **********.077897, "relative_end": **********.077897, "duration": 1.2323501110076904, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.077918, "relative_start": 1.2323710918426514, "end": **********.152126, "relative_end": 3.0994415283203125e-06, "duration": 0.07420802116394043, "duration_str": "74.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 41556040, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET gc/lgn/menu-items-count", "middleware": "web, core, auth", "permission": false, "as": "menu-items-count", "controller": "Shaqi\\Base\\Http\\Controllers\\SystemController@getMenuItemsCount", "namespace": "Shaqi\\Base\\Http\\Controllers", "prefix": "/gc/lgn", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FHttp%2FControllers%2FSystemController.php&line=38\" onclick=\"\">vendor/shaqi/platform/base/src/Http/Controllers/SystemController.php:38-45</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00058, "accumulated_duration_str": "580μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.133561, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "url": "[]", "viewed_case_study": "array:1 [\n  1 => 1752879415\n]"}, "request": {"path_info": "/gc/lgn/menu-items-count", "status_code": "<pre class=sf-dump id=sf-dump-69260283 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-69260283\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-614198770 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-614198770\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-111144392 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111144392\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkVKdXZzVnp2V2xwcXpVZjJBZUVQeHc9PSIsInZhbHVlIjoiSU1RczBkYzYvK2lkMzJ3aGF5ZU8vWmEzS1dwODdKeWpaZW9MMFR5bmlSQWpXTitGNG5td3hSbm5mdnJ2NEZ3czFMRnI4Z2JIcW1ZR1VnS09XM1FIeUFyQUh6YVFUNzdNWjIvemV2U014cThneGY1R2ViNGlhQTcrajcyRmc4SkIiLCJtYWMiOiJkZmYwN2FjYjBkYWNlNmQ5MTA3YTg2ODg3MzhmZDM0YjhkODM0ODEzZTAxY2FmZThjYjc2NGZlOTg0OGQ2ZmIwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJqeUJpUklyZGNINDVwa09NNnJiTGc9PSIsInZhbHVlIjoiZ1picmMxRWU5V2xQb1haQVdaSEtsSVo1dGtFbnAzK3E4T0N6VVU1TThlck9Pb01hODBlQzVCeld0N2UrS29uMFlpSGdqZyttdzQ0K20rcE55WHhCbVZhb05BaXBSVHNEY1ZMa05kYThpbS9PUURhUWZuVkpsT1JzZW1DZ2ViR1NxWVg5aVZDRktqTzF6NkpqSHVKRmtwNWlhdHlNQVp3aWJmWU8rZnBCVURubVVsMjcxTjFvV2VZd01ibTlZc2xKcWRVMHJWNHdwNG1iaThsR1JLWTlEMDRkRGJUQjVlTlZPanpsL1FPMHlMUT0iLCJtYWMiOiIzZTBiNWRhYjZiNmFjOWZlZTVhNDVlNDdkNjRlZGI2NTA0MTZhYjdhZmFjOWJmYTBjYjMwNzA1YmQyZGE3YmUxIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752879417$o44$g0$t1752879417$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkVKdXZzVnp2V2xwcXpVZjJBZUVQeHc9PSIsInZhbHVlIjoiSU1RczBkYzYvK2lkMzJ3aGF5ZU8vWmEzS1dwODdKeWpaZW9MMFR5bmlSQWpXTitGNG5td3hSbm5mdnJ2NEZ3czFMRnI4Z2JIcW1ZR1VnS09XM1FIeUFyQUh6YVFUNzdNWjIvemV2U014cThneGY1R2ViNGlhQTcrajcyRmc4SkIiLCJtYWMiOiJkZmYwN2FjYjBkYWNlNmQ5MTA3YTg2ODg3MzhmZDM0YjhkODM0ODEzZTAxY2FmZThjYjc2NGZlOTg0OGQ2ZmIwIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IktvZWs0VUVRUXdqYWR4M0huQXdCMEE9PSIsInZhbHVlIjoidzFGd21iT1BUdmh1Wm5VbWYvYXpQOHVCcnF4cXUvYWxLSGFJL2oxSC83Rk0wL0JtMlQ2L3ViYXRYbXFKNTI5d3k3ZlE3RHZTL1lTbWZBcXR6djRvNm1wZkk0WTNyQWpodmxqYnJad0E0MTU4eE1ORnJseVJ4S3E3NzQ3TnNyWWwiLCJtYWMiOiJjYjVkZWNjZGI4Zjc0MDc5MTEyYzk0NGIwNTIxMDI5ZmU2NjNiNmRhMzZkMDgwYWRiZmNlNThlZTk4Y2U1ZmEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|93AZBoOuq93wZdZjYcXin3OIxaW8YfC1pd64jsSLusTXT7oOXJKvnP4Pzefq|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pfgazGmSZFAgAHgBDhcfMpRcFY48ppQYxnrLba4f</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-777980834 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 23:04:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IldIU21SVW5nMGxrL0FrMmpvSHpSRWc9PSIsInZhbHVlIjoiUUtHeXlQTVYxdTN5MFY0OTdBNnhxM0IxZXdFMVZEUXNUWFExQUJZVzgralFxd1RqYjlocTNxc09sS2FCWVZHaWp3SHZPYWxURFJyVXE1K3dYQXZlTHkyYzFmcGpvSlFOTGQ0enlCZWF6YzM2QXk2aEd5SmwyanZqdW9BTEx0VEMiLCJtYWMiOiI5MGFlMDljOTJhNjg4YmY2MzM5NDhkYjYzOTk0MWY0MTIxNDg0OTY3MDc1ZjAzODJkZWNlY2JhMmFhM2I4MWU3IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 01:04:43 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IjVoV0t4ekRrNVdmT3pNY01OeHpyZ3c9PSIsInZhbHVlIjoieGxlL3pGWkJMQ3NVUWxiRnZLWk9GY1FkTTB0S0s4TW5Vc3owajVMZVhyNTM4blpwZm9BcWtVcERoeENmYmtmODN0UWJCK0k1dEhSaFAxWGRtenBmU0JOaDl1SU9xbTJ5eERwbGpGYWFXayt1enREUFcwUmVKbEF5dEhkUFhrTzciLCJtYWMiOiIwNTRkMDhlZWI4YmQ0YWNlNmE3MjA1NWM2NGM3MDE4Zjk4ZDQ3YTUwYWE3Yzc3MzcyMWM0ZGUwMzZjYTdkM2U3IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 01:04:43 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IldIU21SVW5nMGxrL0FrMmpvSHpSRWc9PSIsInZhbHVlIjoiUUtHeXlQTVYxdTN5MFY0OTdBNnhxM0IxZXdFMVZEUXNUWFExQUJZVzgralFxd1RqYjlocTNxc09sS2FCWVZHaWp3SHZPYWxURFJyVXE1K3dYQXZlTHkyYzFmcGpvSlFOTGQ0enlCZWF6YzM2QXk2aEd5SmwyanZqdW9BTEx0VEMiLCJtYWMiOiI5MGFlMDljOTJhNjg4YmY2MzM5NDhkYjYzOTk0MWY0MTIxNDg0OTY3MDc1ZjAzODJkZWNlY2JhMmFhM2I4MWU3IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 01:04:43 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IjVoV0t4ekRrNVdmT3pNY01OeHpyZ3c9PSIsInZhbHVlIjoieGxlL3pGWkJMQ3NVUWxiRnZLWk9GY1FkTTB0S0s4TW5Vc3owajVMZVhyNTM4blpwZm9BcWtVcERoeENmYmtmODN0UWJCK0k1dEhSaFAxWGRtenBmU0JOaDl1SU9xbTJ5eERwbGpGYWFXayt1enREUFcwUmVKbEF5dEhkUFhrTzciLCJtYWMiOiIwNTRkMDhlZWI4YmQ0YWNlNmE3MjA1NWM2NGM3MDE4Zjk4ZDQ3YTUwYWE3Yzc3MzcyMWM0ZGUwMzZjYTdkM2U3IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 01:04:43 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777980834\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1719318411 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>viewed_case_study</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1</span> => <span class=sf-dump-num>1752879415</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719318411\", {\"maxDepth\":0})</script>\n"}}