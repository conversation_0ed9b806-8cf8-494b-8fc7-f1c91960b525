<div class="case-studies-widget">
    @if ($caseStudies->isNotEmpty())
        <div class="case-study-list">
            @foreach ($caseStudies as $caseStudy)
                <article class="case-study-item">
                    @if ($caseStudy->image)
                        <div class="case-study-image">
                            <a href="{{ $caseStudy->url }}">
                                <img src="{{ RvMedia::getImageUrl($caseStudy->image) }}" alt="{{ $caseStudy->name }}" class="img-fluid">
                            </a>
                        </div>
                    @endif
                    
                    <div class="case-study-content">
                        <h4><a href="{{ $caseStudy->url }}">{{ $caseStudy->name }}</a></h4>
                        
                        @if ($caseStudy->description)
                            <p>{{ Str::limit($caseStudy->description, 100) }}</p>
                        @endif
                        
                        @if ($caseStudy->client_name)
                            <div class="case-study-client">
                                <strong>{{ __('Client') }}:</strong> {{ $caseStudy->client_name }}
                            </div>
                        @endif
                        
                        @if ($caseStudy->project_url)
                            <a href="{{ $caseStudy->project_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                {{ __('View Project') }}
                            </a>
                        @endif
                        
                        <div class="case-study-meta">
                            <span>{{ $caseStudy->created_at->format('M d, Y') }}</span>
                            
                            @if ($caseStudy->categories->isNotEmpty())
                                <span class="category">{{ $caseStudy->categories->first()->name }}</span>
                            @endif
                        </div>
                    </div>
                </article>
            @endforeach
        </div>
    @endif
</div>
