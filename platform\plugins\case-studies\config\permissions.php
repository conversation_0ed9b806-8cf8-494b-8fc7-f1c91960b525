<?php

return [
    [
        'name' => 'Case Studies',
        'flag' => 'plugins.case-studies',
        'parent_flag' => 'core.cms',
    ],
    [
        'name' => 'Case Studies',
        'flag' => 'case-studies.index',
        'parent_flag' => 'plugins.case-studies',
    ],
    [
        'name' => 'Create',
        'flag' => 'case-studies.create',
        'parent_flag' => 'case-studies.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'case-studies.edit',
        'parent_flag' => 'case-studies.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'case-studies.destroy',
        'parent_flag' => 'case-studies.index',
    ],

    [
        'name' => 'Case Study Categories',
        'flag' => 'case-study-categories.index',
        'parent_flag' => 'plugins.case-studies',
    ],
    [
        'name' => 'Create',
        'flag' => 'case-study-categories.create',
        'parent_flag' => 'case-study-categories.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'case-study-categories.edit',
        'parent_flag' => 'case-study-categories.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'case-study-categories.destroy',
        'parent_flag' => 'case-study-categories.index',
    ],

    [
        'name' => 'Case Studies',
        'flag' => 'case-studies.settings',
        'parent_flag' => 'settings.others',
    ],
    [
        'name' => 'Export Case Studies',
        'flag' => 'case-studies.export',
        'parent_flag' => 'tools.data-synchronize',
    ],
    [
        'name' => 'Import Case Studies',
        'flag' => 'case-studies.import',
        'parent_flag' => 'tools.data-synchronize',
    ],
];
