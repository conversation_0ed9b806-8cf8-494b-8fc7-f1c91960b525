// Case Studies Plugin Styles

.case-study-item {
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    overflow: hidden;
    transition: box-shadow 0.15s ease-in-out;

    &:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .case-study-image {
        img {
            width: 100%;
            height: auto;
            object-fit: cover;
        }
    }

    .case-study-content {
        padding: 1.5rem;

        h4 {
            margin-bottom: 0.75rem;
            
            a {
                color: inherit;
                text-decoration: none;
                
                &:hover {
                    color: #007bff;
                }
            }
        }

        .case-study-client {
            margin: 0.5rem 0;
            font-size: 0.875rem;
            color: #6c757d;
        }

        .case-study-meta {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
            font-size: 0.875rem;
            color: #6c757d;

            .category {
                display: inline-block;
                margin-left: 0.5rem;
                padding: 0.25rem 0.5rem;
                background-color: #f8f9fa;
                border-radius: 0.25rem;
                color: #495057;
                text-decoration: none;
                
                &:hover {
                    background-color: #e9ecef;
                }
            }
        }
    }
}

.case-study-featured-image {
    margin-bottom: 2rem;
    
    img {
        width: 100%;
        height: auto;
        border-radius: 0.375rem;
    }
}

.case-study-client-info {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;

    .client-logo {
        margin-bottom: 1rem;
        
        .client-logo-img {
            max-height: 60px;
            width: auto;
        }
    }
}

.case-study-project-link {
    margin-bottom: 2rem;
}

.case-study-challenge,
.case-study-solution,
.case-study-results {
    margin-bottom: 2rem;
    
    h4 {
        margin-bottom: 1rem;
        color: #495057;
    }
}

.case-study-technologies {
    margin-bottom: 2rem;
    
    h4 {
        margin-bottom: 1rem;
        color: #495057;
    }
    
    ul {
        list-style-type: none;
        padding: 0;
        
        li {
            display: inline-block;
            margin: 0.25rem 0.5rem 0.25rem 0;
            padding: 0.375rem 0.75rem;
            background-color: #007bff;
            color: white;
            border-radius: 1rem;
            font-size: 0.875rem;
        }
    }
}

.case-study-gallery {
    margin-bottom: 2rem;
    
    h4 {
        margin-bottom: 1rem;
        color: #495057;
    }
    
    .row {
        margin: -0.5rem;
        
        > div {
            padding: 0.5rem;
            
            img {
                width: 100%;
                height: 200px;
                object-fit: cover;
                border-radius: 0.375rem;
                cursor: pointer;
                transition: transform 0.15s ease-in-out;
                
                &:hover {
                    transform: scale(1.05);
                }
            }
        }
    }
}

// Admin styles
.case-studies-admin {
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .repeater-item {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
}
