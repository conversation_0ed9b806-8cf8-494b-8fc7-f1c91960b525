<?php
    $page = Theme::get('page');
?>
<?php echo Theme::partial('header'); ?>

<div class="inner-hero" style="background-image: url(<?php echo e(Theme::asset()->url('img/bg/inner-hero-bg.jpg')); ?>);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 m-auto text-center">
                <div class="inner-main-heading">
                    <h1 class="text-white"><?php echo e($page->name); ?></h1>
                    <div class="breadcrumbs-pages">
                        <ul>
                            <li><a href="<?php echo e(url('')); ?>" class="text-white">Home</a></li>
                            <li class="angle"><i class="fa-solid fa-angle-right text-white"></i></li>
                            <li class="text-white"><?php echo e($page->name); ?> </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
   </div>


   <section class="testimonials-section sp">
      <div class="container">
         <div class="heading1 text-center mb-5">
            <span class="sub-title" data-aos="zoom-in-left" data-aos-duration="900"><img src="<?php echo e(Theme::asset()->url('img/icons/span1.svg')); ?>" alt=""> OUR CLIENTS TESTIMONIALS </span>
            <h2 class="text-anime-style-3">What Our Client <span> Say’s </span> </h2>
            <p class="mt-16" data-aos="fade-right" data-aos-duration="900">We are committed to providing our customers with exceptional service while offering our employees the best training.</p>
         </div>

         <div class="row">
            <?php
                // Get testimonials from database
                $testimonials = app(\Shaqi\Testimonial\Repositories\Interfaces\TestimonialInterface::class)
                    ->getModel()
                    ->where('status', 'published')
                    ->get();
            ?>
           <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
           <?php
               $platformLogo = MetaBox::getMetaData($testimonial, 'review_platform_logo', true);
               $companyLogo = MetaBox::getMetaData($testimonial, 'company_logo', true);
               $testimonialTitle = MetaBox::getMetaData($testimonial, 'testimonial_title', true);
           ?>
            <div class="col-12">
                <div class="testimonial-card">
                    <div class="row">
                        <div class="col-md-3">
                            <h3 class="testimonial-title"> <?php echo e($testimonialTitle); ?> </h3>
                            <div class="company-logo-container">
                                <div class="company-logo-inner-container">
                                    <?php if($companyLogo): ?>
                                        <img src="<?php echo e(RvMedia::getImageUrl($companyLogo)); ?>" alt="Company Logo" class="company-logo">
                                    <?php endif; ?>
                                </div>
                            </div>
                            <a href="#" class="view-case-study-btn">View Case Study ➜</a>
                        </div>
                    <div class="col-md-9">
                    <div class="testimonial-header">
                        <div class="star-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>

                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>

                        <div class="testimonial-text">
                            <?php echo $testimonial->content; ?>

                        </div>
                    </div>

                    <div class="testimonial-footer">
                        <div class="user-info">
                             <?php if($testimonial->image): ?>
                                <img class="user-avatar" src="<?php echo e(RvMedia::getImageUrl($testimonial->image, 'thumb', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($testimonial->name); ?>">
                            <?php else: ?>
                                <img class="user-avatar" src="<?php echo e(Theme::asset()->url('img/testimonial/default-avatar.png')); ?>" alt="<?php echo e($testimonial->name); ?>">
                            <?php endif; ?>
                            <div class="platform-info">
                                <?php if($platformLogo): ?>
                                    <img src="<?php echo e(RvMedia::getImageUrl($platformLogo)); ?>" alt="Review Platform" class="platform-logo">
                                <?php endif; ?>
                            </div>
                            <div class="user-details">
                                <h5><?php echo e($testimonial->name); ?></h5>
                                <p class="user-designation"><?php echo e($testimonial->designation); ?></p>
                            </div>
                        </div>

                    </div>
                    </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


        </div>

        <div class="row">
                <div class="col-12">
                    <div class="cta-section">
                        <div class="cta-content">
                            <h3 class="cta-title">Ready to Join Our Success Stories?</h3>
                            <p class="cta-description">
                                Let us help you achieve the same remarkable results and transform your business with our proven strategies.
                            </p>
                            <button class="btn cta-button">
                                Get Started Today
                                <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>


      </div>
  </section>


   <?php echo Theme::partial('footer'); ?>

<?php /**PATH D:\laragon\www\goalconversion\platform\themes/goalconversion/layouts/testimonials.blade.php ENDPATH**/ ?>