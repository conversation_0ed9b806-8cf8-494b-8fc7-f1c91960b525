<?php

namespace Shaqi\CaseStudies\Providers;

use Shaqi\Base\Events\CreatedContentEvent;
use Shaqi\Base\Events\DeletedContentEvent;
use <PERSON>haqi\Base\Events\UpdatedContentEvent;
use <PERSON>haqi\CaseStudies\Listeners\CreatedContentListener;
use <PERSON><PERSON>qi\CaseStudies\Listeners\DeletedContentListener;
use <PERSON>haqi\CaseStudies\Listeners\UpdatedContentListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        UpdatedContentEvent::class => [
            UpdatedContentListener::class,
        ],
        CreatedContentEvent::class => [
            CreatedContentListener::class,
        ],
        DeletedContentEvent::class => [
            DeletedContentListener::class,
        ],
    ];
}
