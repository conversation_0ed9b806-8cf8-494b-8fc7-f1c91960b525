<?php

namespace Shaqi\CaseStudies\Repositories\Interfaces;

use Shaqi\Support\Repositories\Interfaces\RepositoryInterface;
use Shaqi\CaseStudies\Models\CaseStudy;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface CaseStudyInterface extends RepositoryInterface
{
    public function getFeatured(int $limit = 5, array $with = []): Collection;

    public function getListCaseStudyNonInList(array $selected = [], int $limit = 7, array $with = []): Collection;

    public function getRelated(int|string $id, int $limit = 3): Collection;

    public function getRelatedCategoryIds(CaseStudy|int|string $model): array;

    public function getByCategory(array|int|string $categoryId, int $paginate = 12, int $limit = 0): Collection|LengthAwarePaginator;

    public function getByUserId(int|string $authorId, int $paginate = 6): Collection|LengthAwarePaginator;

    public function getDataSiteMap(): Collection|LengthAwarePaginator;

    public function getRecentCaseStudies(int $limit = 5, int|string $categoryId = 0): Collection;

    public function getSearch(?string $keyword, int $limit = 10, int $paginate = 10): Collection|LengthAwarePaginator;

    public function getAllCaseStudies(int $perPage = 12, bool $active = true, array $with = ['slugable']): Collection|LengthAwarePaginator;

    public function getPopularCaseStudies(int $limit, array $args = []): Collection;

    public function getFilters(array $filters): Collection|LengthAwarePaginator;
}
