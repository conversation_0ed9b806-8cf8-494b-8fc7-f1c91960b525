{"__meta": {"id": "X3ec1934d7204f552f3a295d200bce90c", "datetime": "2025-07-18 23:04:37", "utime": **********.927563, "method": "POST", "uri": "/gc/lgn/case-studies/case-studies/edit/1", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752879876.501525, "end": **********.927661, "duration": 1.4261360168457031, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1752879876.501525, "relative_start": 0, "end": **********.668026, "relative_end": **********.668026, "duration": 1.1665010452270508, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.668057, "relative_start": 1.166532039642334, "end": **********.927669, "relative_end": 8.106231689453125e-06, "duration": 0.2596120834350586, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46545888, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST gc/lgn/case-studies/case-studies/edit/{case_study}", "middleware": "web, core, auth", "as": "case-studies.edit.update", "controller": "Shaqi\\CaseStudies\\Http\\Controllers\\CaseStudyController@update", "namespace": "Shaqi\\CaseStudies\\Http\\Controllers", "prefix": "gc/lgn/case-studies/case-studies", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fplugins%2Fcase-studies%2Fsrc%2FHttp%2FControllers%2FCaseStudyController.php&line=73\" onclick=\"\">platform/plugins/case-studies/src/Http/Controllers/CaseStudyController.php:73-86</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02283, "accumulated_duration_str": "22.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.741766, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 3.942}, {"sql": "select * from `case_studies` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 84}, {"index": 18, "namespace": null, "name": "platform/plugins/case-studies/src/Http/Controllers/CaseStudyController.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Http\\Controllers\\CaseStudyController.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.832487, "duration": 0.00912, "duration_str": "9.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 3.942, "width_percent": 39.947}, {"sql": "update `case_studies` set `status` = 'published', `case_studies`.`updated_at` = '2025-07-18 23:04:37' where `id` = 1", "type": "query", "params": [], "bindings": [{"value": "published", "label": "Published"}, "2025-07-18 23:04:37", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/shaqi/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 195}, {"index": 15, "namespace": null, "name": "platform/plugins/case-studies/src/Http/Controllers/CaseStudyController.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Http\\Controllers\\CaseStudyController.php", "line": 79}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.86258, "duration": 0.00661, "duration_str": "6.61ms", "memory": 0, "memory_str": null, "filename": "RepositoriesAbstract.php:195", "source": {"index": 14, "namespace": null, "name": "vendor/shaqi/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 195}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fsupport%2Fsrc%2FRepositories%2FEloquent%2FRepositoriesAbstract.php&line=195", "ajax": false, "filename": "RepositoriesAbstract.php", "line": "195"}, "connection": "goalconversion", "explain": null, "start_percent": 43.89, "width_percent": 28.953}, {"sql": "select count(*) as aggregate from `revisions` where `revisions`.`revisionable_type` = '<PERSON><PERSON><PERSON>\\\\CaseStudies\\\\Models\\\\CaseStudy' and `revisions`.`revisionable_id` = 1 and `revisions`.`revisionable_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\CaseStudies\\Models\\CaseStudy", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/shaqi/revision/src/RevisionableTrait.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\revision\\src\\RevisionableTrait.php", "line": 116}, {"index": 20, "namespace": null, "name": "vendor/shaqi/revision/src/RevisionableTrait.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\revision\\src\\RevisionableTrait.php", "line": 47}, {"index": 27, "namespace": null, "name": "vendor/shaqi/platform/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 195}, {"index": 28, "namespace": null, "name": "platform/plugins/case-studies/src/Http/Controllers/CaseStudyController.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Http\\Controllers\\CaseStudyController.php", "line": 79}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.880118, "duration": 0.0062, "duration_str": "6.2ms", "memory": 0, "memory_str": null, "filename": "RevisionableTrait.php:116", "source": {"index": 19, "namespace": null, "name": "vendor/shaqi/revision/src/RevisionableTrait.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\revision\\src\\RevisionableTrait.php", "line": 116}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Frevision%2Fsrc%2FRevisionableTrait.php&line=116", "ajax": false, "filename": "RevisionableTrait.php", "line": "116"}, "connection": "goalconversion", "explain": null, "start_percent": 72.843, "width_percent": 27.157}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Shaqi\\CaseStudies\\Models\\CaseStudy": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fplugins%2Fcase-studies%2Fsrc%2FModels%2FCaseStudy.php&line=1", "ajax": false, "filename": "CaseStudy.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/case-study/long-term-rental-2-room-apartment-with-air-conditioning-and-parking-krakow\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success_msg\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "url": "[]", "viewed_case_study": "array:1 [\n  1 => 1752879415\n]", "success_msg": "Updated successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/gc/lgn/case-studies/case-studies/edit/1", "status_code": "<pre class=sf-dump id=sf-dump-1903035495 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1903035495\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1709959083 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1709959083\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1637756110 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Long-term rental 2-room apartment with air conditioning and parking, Krak&#243;w</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Shaqi\\CaseStudies\\Models\\CaseStudy</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"74 characters\">long-term-rental-2-room-apartment-with-air-conditioning-and-parking-krakow</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"400 characters\">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s w</span>\"\n  \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>content</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"617 characters\">&lt;h2&gt;What is Lorem Ipsum?&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"617 characters\">&lt;p&gt;Lorem Ipsum&amp;nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.&lt;/p&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"47 characters\">portfolio/screen-shot-2025-07-17-at-031000.webp</span>\"\n  \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Mad Lab Auto</span>\"\n  \"<span class=sf-dump-key>client_logo</span>\" => \"<span class=sf-dump-str title=\"23 characters\">portfolio/madlab-1.webp</span>\"\n  \"<span class=sf-dump-key>project_url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://goalconversion.gc/</span>\"\n  \"<span class=sf-dump-key>challenge</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"612 characters\">&lt;h2&gt;What is Lorem Ipsum?&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"612 characters\">&lt;p&gt;Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.&lt;/p&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>solution</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"612 characters\">&lt;h2&gt;What is Lorem Ipsum?&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"612 characters\">&lt;p&gt;Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.&lt;/p&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>results</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"612 characters\">&lt;h2&gt;What is Lorem Ipsum?&lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"612 characters\">&lt;p&gt;Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.&lt;/p&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>technologies</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>key</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>key</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"6 characters\">test 2</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"47 characters\">portfolio/screen-shot-2025-07-17-at-031449.webp</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"47 characters\">portfolio/screen-shot-2025-07-17-at-031000.webp</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"47 characters\">portfolio/screen-shot-2025-07-17-at-031745.webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637756110\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2017175258 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4157</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJqeUJpUklyZGNINDVwa09NNnJiTGc9PSIsInZhbHVlIjoiZ1picmMxRWU5V2xQb1haQVdaSEtsSVo1dGtFbnAzK3E4T0N6VVU1TThlck9Pb01hODBlQzVCeld0N2UrS29uMFlpSGdqZyttdzQ0K20rcE55WHhCbVZhb05BaXBSVHNEY1ZMa05kYThpbS9PUURhUWZuVkpsT1JzZW1DZ2ViR1NxWVg5aVZDRktqTzF6NkpqSHVKRmtwNWlhdHlNQVp3aWJmWU8rZnBCVURubVVsMjcxTjFvV2VZd01ibTlZc2xKcWRVMHJWNHdwNG1iaThsR1JLWTlEMDRkRGJUQjVlTlZPanpsL1FPMHlMUT0iLCJtYWMiOiIzZTBiNWRhYjZiNmFjOWZlZTVhNDVlNDdkNjRlZGI2NTA0MTZhYjdhZmFjOWJmYTBjYjMwNzA1YmQyZGE3YmUxIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752879417$o44$g0$t1752879417$j60$l0$h0; XSRF-TOKEN=eyJpdiI6ImRzVmZqU0NSZDJHemhhekVMRXZNUEE9PSIsInZhbHVlIjoidUtBcENydzBBMFRpU0lKVDNCNlNWb3hUVXEzSXBvd1NvTFRMQVAxbk1PeDJUUnd1OGU5ZDFSNVJvaFAvRVBxT0kyaE1peUJKQi9uQStWZlVvc0dKWlBkdjQ3OG9ZM1BRWUhZdm5IbFd3RHZtWnB2NUxVTktITkJEU09odzZORFEiLCJtYWMiOiI3MjQ2YWFhMWIxMWRmZDYwN2I1ZDFkNTE2NmVjMDQ2YjdiODFhNTkwYTk3Y2U2OWE1YmYzYjk3YWY0Mjg2YjM4IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IjhpWUZEL0ZGWE5CNW9mbERHbzlZYkE9PSIsInZhbHVlIjoidTJGV0xSNUpyMVNDUE8vNDMrMlJscHhkVGlCQ3U1WHZiVjBUVjlPYWVZZ3pqZ3RKRGZ5Z2Q1SExxM1RnSnA2NVFTdkV3TFdkYmhoMGl5TkYrQlNtc2p6R1NkNHdsTHd4dDNoSm9RU1NWSldFRWh0VUZXbDlaMnR4b08zQ0NYRGMiLCJtYWMiOiIyYzM2NDVmMjFiNTRiOGU0NmE2MDUxZjdmYzViNTA0OTFiY2QwMDlmMDk0YjdiMDFkNjY2NTdlYzBhNzU2NmFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017175258\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-871267731 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|93AZBoOuq93wZdZjYcXin3OIxaW8YfC1pd64jsSLusTXT7oOXJKvnP4Pzefq|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pfgazGmSZFAgAHgBDhcfMpRcFY48ppQYxnrLba4f</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871267731\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1046888581 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 23:04:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IitGUll4dnIyM2RiTVFhci9FbHRjeHc9PSIsInZhbHVlIjoiZ242V1hBSWRxK2tGVWEwRUU4bnVlSFJ6QU1ndk9mZElQUzhOWW9JbzI2K0UxcktsNm10YlBSZmNjUUljN25YZitPMVE3SmtHV1pmMDJobnYxNjJXRjd5c2g3SzJXd1UwOU5QVUkzS1ZkdUU2aVJDc1lETTc5cmxoalBKa09MR3AiLCJtYWMiOiI1YmFmYTY1M2MyNjJjYTExNmRmODBlOTBhNGJmNjRmZTJmM2IyMWRmNzc4NGZiN2EwYTg0YWI1ZGZjZTdlODMzIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 01:04:37 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IktNVTgxdXozRHo0SjlWc1Y3NWNUY1E9PSIsInZhbHVlIjoicFhiMUFYZURZVXI3OElsVVIrTTdzdnNtKy9pQ2hXUTNmQUo1djFyT1Vvb0dpRmQrc2JnQXJNSkhHTzA1NC82SEQrV3Q2eDFPNndET2NvT21qMGt0ZEhmSEVoRnRrL3lWODRVR0F1NFJwQ2wwaHdKTXBOcXp1SkhHUHRRaWFjYmoiLCJtYWMiOiI4M2NmZGQ3YTc3YjEwZGIzYzRhZDIyYTZlNTZiOGI5NDUyMDFjMTljMjdkZjdkNzczNTc2YTA0NjZiMGRiNjg4IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 01:04:37 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IitGUll4dnIyM2RiTVFhci9FbHRjeHc9PSIsInZhbHVlIjoiZ242V1hBSWRxK2tGVWEwRUU4bnVlSFJ6QU1ndk9mZElQUzhOWW9JbzI2K0UxcktsNm10YlBSZmNjUUljN25YZitPMVE3SmtHV1pmMDJobnYxNjJXRjd5c2g3SzJXd1UwOU5QVUkzS1ZkdUU2aVJDc1lETTc5cmxoalBKa09MR3AiLCJtYWMiOiI1YmFmYTY1M2MyNjJjYTExNmRmODBlOTBhNGJmNjRmZTJmM2IyMWRmNzc4NGZiN2EwYTg0YWI1ZGZjZTdlODMzIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 01:04:37 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IktNVTgxdXozRHo0SjlWc1Y3NWNUY1E9PSIsInZhbHVlIjoicFhiMUFYZURZVXI3OElsVVIrTTdzdnNtKy9pQ2hXUTNmQUo1djFyT1Vvb0dpRmQrc2JnQXJNSkhHTzA1NC82SEQrV3Q2eDFPNndET2NvT21qMGt0ZEhmSEVoRnRrL3lWODRVR0F1NFJwQ2wwaHdKTXBOcXp1SkhHUHRRaWFjYmoiLCJtYWMiOiI4M2NmZGQ3YTc3YjEwZGIzYzRhZDIyYTZlNTZiOGI5NDUyMDFjMTljMjdkZjdkNzczNTc2YTA0NjZiMGRiNjg4IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 01:04:37 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046888581\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-851247693 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"111 characters\">https://goalconversion.gc/case-study/long-term-rental-2-room-apartment-with-air-conditioning-and-parking-krakow</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>viewed_case_study</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1</span> => <span class=sf-dump-num>1752879415</span>\n  </samp>]\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851247693\", {\"maxDepth\":0})</script>\n"}}