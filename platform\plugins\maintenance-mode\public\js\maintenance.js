/******/ (() => { // webpackBootstrap
var __webpack_exports__ = {};
/*!******************************************************************************!*\
  !*** ./platform/plugins/maintenance-mode/resources/assets/js/maintenance.js ***!
  \******************************************************************************/
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
var MaintenanceMode = /*#__PURE__*/function () {
  function MaintenanceMode() {
    _classCallCheck(this, MaintenanceMode);
  }
  _createClass(MaintenanceMode, [{
    key: "init",
    value: function init() {
      $(document).on('click', '#btn-maintenance', function (event) {
        event.preventDefault();
        var _self = $(event.currentTarget);
        _self.addClass('button-loading');
        $.ajax({
          type: 'POST',
          url: route('system.maintenance.run'),
          cache: false,
          data: _self.closest('form').serialize(),
          success: function success(res) {
            if (!res.error) {
              Shaqi.showSuccess(res.message);
              var data = res.data;
              _self.text(data.message);
              if (!data.is_down) {
                _self.removeClass('btn-warning').addClass('btn-info');
                _self.closest('form').find('.maintenance-mode-notice div span').removeClass('text-danger').addClass('text-success').text(data.notice);
              } else {
                _self.addClass('btn-warning').removeClass('btn-info');
                _self.closest('form').find('.maintenance-mode-notice div span').addClass('text-danger').removeClass('text-success').text(data.notice);
                if (data.url) {
                  $('#bypassMaintenanceMode .maintenance-mode-bypass').attr('href', data.url);
                  $('#bypassMaintenanceMode #secret-link').val(data.url);
                  $('#bypassMaintenanceMode').modal('show');
                }
              }
            } else {
              Shaqi.showError(res.message);
            }
          },
          error: function error(res) {
            Shaqi.handleError(res);
          },
          complete: function complete() {
            _self.removeClass('button-loading');
          }
        });
      });
    }
  }]);
  return MaintenanceMode;
}();
$(document).ready(function () {
  new MaintenanceMode().init();
});
/******/ })()
;
//# sourceMappingURL=maintenance.js.map