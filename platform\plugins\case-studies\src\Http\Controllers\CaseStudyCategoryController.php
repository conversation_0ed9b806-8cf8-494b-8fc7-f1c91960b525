<?php

namespace Shaqi\CaseStudies\Http\Controllers;

use Shaqi\Base\Events\BeforeEditContentEvent;
use Shaqi\Base\Events\CreatedContentEvent;
use Shaqi\Base\Events\DeletedContentEvent;
use Shaqi\Base\Events\UpdatedContentEvent;
use <PERSON>haqi\Base\Facades\Assets;
use Shaqi\Base\Facades\PageTitle;
use Shaqi\Base\Forms\FormBuilder;
use Shaqi\Base\Http\Controllers\BaseController;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\Base\Supports\Helper;
use Shaqi\CaseStudies\Forms\CaseStudyCategoryForm;
use Shaqi\CaseStudies\Http\Requests\CaseStudyCategoryRequest;
use Shaqi\CaseStudies\Models\CaseStudyCategory;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyCategoryInterface;
use Shaqi\Support\Repositories\Interfaces\RepositoryInterface;
use Exception;
use Illuminate\Http\Request;

class CaseStudyCategoryController extends BaseController
{
    public function __construct(protected CaseStudyCategoryInterface $categoryRepository)
    {
    }

    public function index(Request $request)
    {
        PageTitle::setTitle(trans('plugins/case-studies::categories.menu'));

        $categories = CaseStudyCategory::query()
            ->orderByDesc('is_default')
            ->oldest('order')->oldest()
            ->with('slugable');

        $categories = Helper::applyBeforeExecuteQuery($categories, new CaseStudyCategory())->get();

        if ($request->ajax()) {
            $data = view('core/base::forms.partials.tree-categories', $this->getOptions(compact('categories')))
                ->render();

            return $this
                ->httpResponse()
                ->setData($data);
        }

        Assets::addStylesDirectly('vendor/core/core/base/css/tree-category.css')
            ->addScriptsDirectly('vendor/core/core/base/js/tree-category.js');

        return view('plugins/case-studies::categories.index', compact('categories'));
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/case-studies::categories.create'));

        return $formBuilder->create(CaseStudyCategoryForm::class)->renderForm();
    }

    public function store(CaseStudyCategoryRequest $request, BaseHttpResponse $response)
    {
        $category = $this->categoryRepository->createOrUpdate($request->input());

        event(new CreatedContentEvent(CASE_STUDY_CATEGORY_MODULE_SCREEN_NAME, $request, $category));

        return $response
            ->setPreviousUrl(route('case-study-categories.index'))
            ->setNextUrl(route('case-study-categories.edit', $category->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(int|string $id, FormBuilder $formBuilder, Request $request)
    {
        $category = $this->categoryRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $category));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $category->name]));

        return $formBuilder->create(CaseStudyCategoryForm::class, ['model' => $category])->renderForm();
    }

    public function update(int|string $id, CaseStudyCategoryRequest $request, BaseHttpResponse $response)
    {
        $category = $this->categoryRepository->findOrFail($id);

        $category->fill($request->input());

        $this->categoryRepository->createOrUpdate($category);

        event(new UpdatedContentEvent(CASE_STUDY_CATEGORY_MODULE_SCREEN_NAME, $request, $category));

        return $response
            ->setPreviousUrl(route('case-study-categories.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(int|string $id, Request $request, BaseHttpResponse $response)
    {
        try {
            $category = $this->categoryRepository->findOrFail($id);

            $this->categoryRepository->delete($category);

            event(new DeletedContentEvent(CASE_STUDY_CATEGORY_MODULE_SCREEN_NAME, $request, $category));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    protected function getOptions(array $data = []): array
    {
        return [
            'canCreate' => true,
            'canEdit' => true,
            'canDelete' => true,
            'createRoute' => 'case-study-categories.create',
            'editRoute' => 'case-study-categories.edit',
            'deleteRoute' => 'case-study-categories.destroy',
        ] + $data;
    }
}
