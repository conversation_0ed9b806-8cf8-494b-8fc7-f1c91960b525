/* Case Studies Plugin Styles */

.case-study-item {
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    overflow: hidden;
    transition: box-shadow 0.15s ease-in-out;
}

.case-study-item:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.case-study-item .case-study-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

.case-study-item .case-study-content {
    padding: 1.5rem;
}

.case-study-item .case-study-content h4 {
    margin-bottom: 0.75rem;
}

.case-study-item .case-study-content h4 a {
    color: inherit;
    text-decoration: none;
}

.case-study-item .case-study-content h4 a:hover {
    color: #007bff;
}

.case-study-item .case-study-content .case-study-client {
    margin: 0.5rem 0;
    font-size: 0.875rem;
    color: #6c757d;
}

.case-study-item .case-study-content .case-study-meta {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    font-size: 0.875rem;
    color: #6c757d;
}

.case-study-item .case-study-content .case-study-meta .category {
    display: inline-block;
    margin-left: 0.5rem;
    padding: 0.25rem 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    color: #495057;
    text-decoration: none;
}

.case-study-item .case-study-content .case-study-meta .category:hover {
    background-color: #e9ecef;
}

.case-study-featured-image {
    margin-bottom: 2rem;
}

.case-study-featured-image img {
    width: 100%;
    height: auto;
    border-radius: 0.375rem;
}

.case-study-client-info {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.case-study-client-info .client-logo {
    margin-bottom: 1rem;
}

.case-study-client-info .client-logo .client-logo-img {
    max-height: 60px;
    width: auto;
}

.case-study-project-link {
    margin-bottom: 2rem;
}

.case-study-challenge,
.case-study-solution,
.case-study-results {
    margin-bottom: 2rem;
}

.case-study-challenge h4,
.case-study-solution h4,
.case-study-results h4 {
    margin-bottom: 1rem;
    color: #495057;
}

.case-study-technologies {
    margin-bottom: 2rem;
}

.case-study-technologies h4 {
    margin-bottom: 1rem;
    color: #495057;
}

.case-study-technologies ul {
    list-style-type: none;
    padding: 0;
}

.case-study-technologies ul li {
    display: inline-block;
    margin: 0.25rem 0.5rem 0.25rem 0;
    padding: 0.375rem 0.75rem;
    background-color: #007bff;
    color: white;
    border-radius: 1rem;
    font-size: 0.875rem;
}

.case-study-gallery {
    margin-bottom: 2rem;
}

.case-study-gallery h4 {
    margin-bottom: 1rem;
    color: #495057;
}

.case-study-gallery .row {
    margin: -0.5rem;
}

.case-study-gallery .row > div {
    padding: 0.5rem;
}

.case-study-gallery .row > div img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: transform 0.15s ease-in-out;
}

.case-study-gallery .row > div img:hover {
    transform: scale(1.05);
}

.case-studies-admin .form-group {
    margin-bottom: 1.5rem;
}

.case-studies-admin .repeater-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}
