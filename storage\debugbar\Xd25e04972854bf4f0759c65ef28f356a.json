{"__meta": {"id": "Xd25e04972854bf4f0759c65ef28f356a", "datetime": "2025-07-18 20:47:28", "utime": **********.563313, "method": "GET", "uri": "/testimonials", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752871647.290438, "end": **********.56336, "duration": 1.2729220390319824, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1752871647.290438, "relative_start": 0, "end": **********.120559, "relative_end": **********.120559, "duration": 0.8301210403442383, "duration_str": "830ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120574, "relative_start": 0.8301360607147217, "end": **********.563366, "relative_end": 5.9604644775390625e-06, "duration": 0.4427919387817383, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45212688, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "theme.goalconversion::views.page", "param_count": null, "params": [], "start": **********.191782, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/views/page.blade.phptheme.goalconversion::views.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fviews%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}}, {"name": "theme.goalconversion::layouts.testimonials", "param_count": null, "params": [], "start": **********.193932, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.phptheme.goalconversion::layouts.testimonials", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Flayouts%2Ftestimonials.blade.php&line=1", "ajax": false, "filename": "testimonials.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.header", "param_count": null, "params": [], "start": **********.194577, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/header.blade.phptheme.goalconversion::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "packages/theme::partials.header", "param_count": null, "params": [], "start": **********.196089, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.footer", "param_count": null, "params": [], "start": **********.253216, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/footer.blade.phptheme.goalconversion::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.553005, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET {slug?}", "middleware": "web, core", "controller": "Shaqi\\Theme\\Http\\Controllers\\PublicController@getView", "namespace": null, "prefix": "", "where": [], "as": "public.single", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php&line=44\" onclick=\"\">vendor/shaqi/theme/src/Http/Controllers/PublicController.php:44-93</a>"}, "queries": {"nb_statements": 14, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0072499999999999995, "accumulated_duration_str": "7.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `slugs` where (`key` = 'testimonials' and `prefix` = '') limit 1", "type": "query", "params": [], "bindings": ["testimonials", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/slug/src/SlugHelper.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\SlugHelper.php", "line": 182}, {"index": 19, "namespace": null, "name": "vendor/shaqi/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.159911, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 8}, {"sql": "select * from `pages` where (`id` = 19 and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": [19, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Services\\PageService.php", "line": 37}, {"index": 18, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.170063, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 8, "width_percent": 7.862}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (19) and `slugs`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/shaqi/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Services\\PageService.php", "line": 37}, {"index": 24, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.175743, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 15.862, "width_percent": 6.483}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (19) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "vendor/shaqi/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 87}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 30, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.184622, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 22.345, "width_percent": 7.31}, {"sql": "select * from `testimonials` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.2218142, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 29.655, "width_percent": 6.345}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'review_platform_logo' and `reference_id` = 1 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["review_platform_logo", 1, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.2239451, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 36, "width_percent": 4.552}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'company_logo' and `reference_id` = 1 and `reference_type` = '<PERSON>haqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["company_logo", 1, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 45}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.2255352, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 40.552, "width_percent": 4.138}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'testimonial_title' and `reference_id` = 1 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["testimonial_title", 1, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.22698, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 44.69, "width_percent": 4.138}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'review_platform_logo' and `reference_id` = 2 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["review_platform_logo", 2, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.23794, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 48.828, "width_percent": 7.31}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'company_logo' and `reference_id` = 2 and `reference_type` = '<PERSON>haqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["company_logo", 2, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 45}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.2397048, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 56.138, "width_percent": 5.379}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'testimonial_title' and `reference_id` = 2 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["testimonial_title", 2, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.241224, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 61.517, "width_percent": 5.241}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'review_platform_logo' and `reference_id` = 3 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["review_platform_logo", 3, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.244632, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 66.759, "width_percent": 5.379}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'company_logo' and `reference_id` = 3 and `reference_type` = '<PERSON>haqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["company_logo", 3, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 45}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.24618, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 72.138, "width_percent": 11.31}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'testimonial_title' and `reference_id` = 3 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["testimonial_title", 3, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.248202, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 83.448, "width_percent": 16.552}]}, "models": {"data": {"Shaqi\\Base\\Models\\MetaBox": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Shaqi\\Testimonial\\Models\\Testimonial": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fplugins%2Ftestimonial%2Fsrc%2FModels%2FTestimonial.php&line=1", "ajax": false, "filename": "Testimonial.php", "line": "?"}}, "Shaqi\\Slug\\Models\\Slug": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Shaqi\\Page\\Models\\Page": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fpage%2Fsrc%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}}, "count": 17, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/testimonials\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/testimonials", "status_code": "<pre class=sf-dump id=sf-dump-1293519906 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1293519906\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1602576957 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1602576957\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-678132555 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-678132555\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-70816006 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">https://goalconversion.gc/portfolio</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"828 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; XSRF-TOKEN=eyJpdiI6IkNjQXpkaXppbWgzVndFa1FQQlBLalE9PSIsInZhbHVlIjoiRkZzNFE3UUxteFIrQW94YmhRU3VwRy9IbmhDYzdjcS9udjJ0WmVWSVI3T3VabStPcnhzWXVhNGswTmNTK1hLRnI1N1U1Q1ZrcktBSHc2eFdTUnk2WC80RllRam1hWGN2RlM5My9jK00wUTFvbWQ0RUttV0diZFZ6WnNNb0NIQVMiLCJtYWMiOiJlNDlhZDBlZDkzMTFlNWFlMzA4MzQzMDRiZDBiZDVkZDY3NmEwZGJjZGRiMWQ4YWYyMTBmMmU0ZTJlYjYyZGJlIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IkVZUGxSRkY0Q0tZZnRFdytJZ20wREE9PSIsInZhbHVlIjoiVG9HWlcyR1ArTjV4OG9xNkk5dW0vTHVaU0lMTkVXT2U1NmJmU2VtNXhmelVUR2tUWEttb0NoelRwQ1M1UzRLN0xOS29PNUwzNHdWSmFQcjcyYytsN2ZZdTRYMndHcUNuTkJLZURMbHYyeXUySzFOMTdKM01kc2tCbkd6WUNubmIiLCJtYWMiOiI1Y2RiYTA4NDMyYzA4ZDNhMThkOGYxNmY3MTMwNDE5YzhmZDcwOGU5OTliYzE1ZjgwYWRjNjMxOGUzNjRkMDI2IiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752869141$o43$g1$t1752871493$j58$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70816006\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-957310279 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mHLAZjM1JlnDMgXjdTz81tKLTfRvwSGOZHTC25kf</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-957310279\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 20:47:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cms-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.5.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization-at</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>activated-license</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">Yes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6Ik5CN0ZwYnA2cWVsUjUrdG5pOS9EVEE9PSIsInZhbHVlIjoiVEFTeG9lNFE2VnVHcDJYdUpKNHZYRktaZEhsL2VZQXJIV2ZYTVhYUEpHUFFGYlMwRVMxWExsN2paMS8rL1hzdnkvSzNDR29UdFpDMzJIVmhKTEozZkhiUzZaUW5GQnFpZ0ZSRkU3TkM5VDlKVEx3MEszSXhlc2d0RVpoT1lCZWoiLCJtYWMiOiI3M2ZiMWEyZjM1ZDUyYTU4NGVlMzZjNWFkZTM4MmRkMzI2MjA3NmEyYjAyOWM2N2IxMjFmZmFiNjhkYmE1ZTRhIiwidGFnIjoiIn0%3D; expires=Fri, 18 Jul 2025 22:47:28 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6ImorYVE3SHpobTJkNUtnaGJGWDFUWkE9PSIsInZhbHVlIjoiY3pFN283Q1FDZit1bDBzU1hObjhBb0hRSFlXR1RCWkdVVmJaNjA5OWRzbmFoZVk3c1dpYWdhakNCNUtUdUFWQUhNcDh2K0tEb3pMMnpidUFhTHNYdmwyVE5DOXhWdmhnaHhFdFNtQUNiSDM0czNJM0p5Z0hNa1hYRHBFdDlFMkYiLCJtYWMiOiI5OWFmZDkyMTlhYWJmMTRmNjg5ZDdjMTdkMmU5ZmE0MjgzNzVhZmViYjcwN2QyZTlkY2U1YzVkY2MzNTZjOTYwIiwidGFnIjoiIn0%3D; expires=Fri, 18 Jul 2025 22:47:28 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6Ik5CN0ZwYnA2cWVsUjUrdG5pOS9EVEE9PSIsInZhbHVlIjoiVEFTeG9lNFE2VnVHcDJYdUpKNHZYRktaZEhsL2VZQXJIV2ZYTVhYUEpHUFFGYlMwRVMxWExsN2paMS8rL1hzdnkvSzNDR29UdFpDMzJIVmhKTEozZkhiUzZaUW5GQnFpZ0ZSRkU3TkM5VDlKVEx3MEszSXhlc2d0RVpoT1lCZWoiLCJtYWMiOiI3M2ZiMWEyZjM1ZDUyYTU4NGVlMzZjNWFkZTM4MmRkMzI2MjA3NmEyYjAyOWM2N2IxMjFmZmFiNjhkYmE1ZTRhIiwidGFnIjoiIn0%3D; expires=Fri, 18-Jul-2025 22:47:28 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6ImorYVE3SHpobTJkNUtnaGJGWDFUWkE9PSIsInZhbHVlIjoiY3pFN283Q1FDZit1bDBzU1hObjhBb0hRSFlXR1RCWkdVVmJaNjA5OWRzbmFoZVk3c1dpYWdhakNCNUtUdUFWQUhNcDh2K0tEb3pMMnpidUFhTHNYdmwyVE5DOXhWdmhnaHhFdFNtQUNiSDM0czNJM0p5Z0hNa1hYRHBFdDlFMkYiLCJtYWMiOiI5OWFmZDkyMTlhYWJmMTRmNjg5ZDdjMTdkMmU5ZmE0MjgzNzVhZmViYjcwN2QyZTlkY2U1YzVkY2MzNTZjOTYwIiwidGFnIjoiIn0%3D; expires=Fri, 18-Jul-2025 22:47:28 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-296471216 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://goalconversion.gc/testimonials</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296471216\", {\"maxDepth\":0})</script>\n"}}