// Case Studies Plugin JavaScript

$(document).ready(function() {
    // Gallery lightbox functionality
    if (typeof $.fn.magnificPopup !== 'undefined') {
        $('.case-study-gallery img').magnificPopup({
            type: 'image',
            gallery: {
                enabled: true
            },
            zoom: {
                enabled: true,
                duration: 300
            }
        });
    }

    // Technology tags interaction
    $('.case-study-technologies li').on('click', function() {
        const technology = $(this).text();
        // Could be used for filtering or search functionality
        console.log('Technology clicked:', technology);
    });

    // Case study item hover effects
    $('.case-study-item').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );

    // Admin form enhancements
    if ($('.case-studies-admin').length) {
        // Auto-generate slug from name
        $('#name').on('keyup', function() {
            const name = $(this).val();
            const slug = name.toLowerCase()
                .replace(/[^\w ]+/g, '')
                .replace(/ +/g, '-');
            $('#slug').val(slug);
        });

        // Preview image upload
        $('.image-upload').on('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $(this).siblings('.image-preview').attr('src', e.target.result);
                }.bind(this);
                reader.readAsDataURL(file);
            }
        });
    }
});
