{"__meta": {"id": "X1ad673a5d3282ef887e26e8bcedf8750", "datetime": "2025-07-18 22:46:26", "utime": **********.903537, "method": "GET", "uri": "/gc/lgn/login", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752878785.567499, "end": **********.903593, "duration": 1.3360941410064697, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1752878785.567499, "relative_start": 0, "end": **********.752851, "relative_end": **********.752851, "duration": 1.185352087020874, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.752893, "relative_start": 1.185394048690796, "end": **********.903598, "relative_end": 5.0067901611328125e-06, "duration": 0.15070509910583496, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 41721880, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET gc/lgn/login", "middleware": "web, core, guest", "controller": "Shaqi\\ACL\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "Shaqi\\ACL\\Http\\Controllers", "prefix": "/gc/lgn", "where": [], "as": "access.login", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=29\" onclick=\"\">vendor/shaqi/platform/acl/src/Http/Controllers/Auth/LoginController.php:29-34</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01696, "accumulated_duration_str": "16.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}], "start": **********.830498, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 19.929}, {"sql": "select * from `user_meta` where `user_meta`.`user_id` = 1 and `user_meta`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 62}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\acl\\src\\Concerns\\HasPreferences.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 99}, {"index": 20, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\AdminAppearance.php", "line": 39}], "start": **********.844168, "duration": 0.01358, "duration_str": "13.58ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 19.929, "width_percent": 80.071}]}, "models": {"data": {"Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "url": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/gc/lgn/login", "status_code": "<pre class=sf-dump id=sf-dump-2066042305 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2066042305\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1320057347 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1320057347\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1068844749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1068844749\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-476054283 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">https://goalconversion.gc/gc/lgn/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; _ga_FSKD72EXSB=GS2.1.s1752869141$o43$g1$t1752874702$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Img4S2Q5dVkxL1JmYVdSd2hJdzVYRmc9PSIsInZhbHVlIjoidXExdUtRWU1YamZHR2VybEorV00zaUEyUnJ6SmkvWncrcm9hdUgwcDRyVVRxTUtIbEN5eUZyMzdZY3BFb25EM2pESzA0RS94NjNwMEJuUG1EREliRVpKRndWLzJUQmZiTzB1NGlQY2Njb2dZWmxVOGdsZW8vd2tGWU5rTjRwWC8iLCJtYWMiOiI1ZDlmZjQ0MDllMDNmYzEyMTgyZThkOTM4YjBmMjc5YWQzMGU3YTE4YzA5MDVhN2RmYWNmNjY4M2ZlYjM1OWE5IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6InllUzVhd3BqRi8rVjJJempyc0JObGc9PSIsInZhbHVlIjoidjlFWllPV1lYQ1hHN3V0SlJLZHpPdzNtd20zaWZrZHpFcUM4ZmZMQnZqVUtGcjNJUE52UnU1RmNQZXVWT1BaQTUvUnJmYm1JMzZkUnBoNk5pV01qTmZYMUx6Q1VTK2ViL1k1bFNyWk5pM3pHTTdFWEpFM1MrM0RDSCs1d2FzT2IiLCJtYWMiOiJiMzhiMWQ5NzM2Zjg1MWQxYzRiOWE2ODM0ZjU1OWY0NDQwYmMyNWM3NDllMDI5NzgzMzllZTdkMmE3MTNhNTY0IiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJqeUJpUklyZGNINDVwa09NNnJiTGc9PSIsInZhbHVlIjoiZ1picmMxRWU5V2xQb1haQVdaSEtsSVo1dGtFbnAzK3E4T0N6VVU1TThlck9Pb01hODBlQzVCeld0N2UrS29uMFlpSGdqZyttdzQ0K20rcE55WHhCbVZhb05BaXBSVHNEY1ZMa05kYThpbS9PUURhUWZuVkpsT1JzZW1DZ2ViR1NxWVg5aVZDRktqTzF6NkpqSHVKRmtwNWlhdHlNQVp3aWJmWU8rZnBCVURubVVsMjcxTjFvV2VZd01ibTlZc2xKcWRVMHJWNHdwNG1iaThsR1JLWTlEMDRkRGJUQjVlTlZPanpsL1FPMHlMUT0iLCJtYWMiOiIzZTBiNWRhYjZiNmFjOWZlZTVhNDVlNDdkNjRlZGI2NTA0MTZhYjdhZmFjOWJmYTBjYjMwNzA1YmQyZGE3YmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476054283\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1534752089 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pfgazGmSZFAgAHgBDhcfMpRcFY48ppQYxnrLba4f</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|93AZBoOuq93wZdZjYcXin3OIxaW8YfC1pd64jsSLusTXT7oOXJKvnP4Pzefq|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534752089\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-203950066 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 22:46:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://goalconversion.gc/gc/lgn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6InppWkdxdHROZ0VNV3Fsa0lmdUhDdVE9PSIsInZhbHVlIjoiZk1rWWpwTjg3QjI1aHd5N1BwYkM2YXJhU1pzcUlZZGxxOVlVQ2YwL05qS29ZZXZ0bVI0WE5VR0U0TnV2RVdqMjhhU1VBUUhUTzlPRUc0RExPNEtsd084ZHpYMWwxMnBlUTY2dnJFYVZ2OUFNcXpiZjBUK3ZoTzJ2VWowVERWOVEiLCJtYWMiOiIyY2NjNzliYzk0ZTk2OGU3ZjQzNjk4NzhiOTkxZjJiZmRjZThlMjg0MzA2MTIyNDlkY2QwYWRhYzQxNDZlNTNlIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:46:26 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IkhmNmtZVmhCUXo2QnpSN0d6a3hOeEE9PSIsInZhbHVlIjoia1RUOE9UK09BdmZhNXYyZTkwY2IwQ3VjZG1vYm0xT21LOWJwUnU4M0dHT2xzaDEycUFOY2h3YStZbXpxclM0QnNGMldpRmdCTDU3YjBUUkl5dENzK2oyblRvc0hFaEFiY0t0UDVCcmM4ckVKOFk2M0ZWOEtFUGIveFZodnlPWEEiLCJtYWMiOiI4ZjAxNmJiMGFlOGQ4MjFhYzcwMTI1ZGRjNjYyYTUyNGQzYmQ1MjI2ODA5NTNkZDcyNTNiMjMwOTg5OTFmYWQ2IiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:46:26 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6InppWkdxdHROZ0VNV3Fsa0lmdUhDdVE9PSIsInZhbHVlIjoiZk1rWWpwTjg3QjI1aHd5N1BwYkM2YXJhU1pzcUlZZGxxOVlVQ2YwL05qS29ZZXZ0bVI0WE5VR0U0TnV2RVdqMjhhU1VBUUhUTzlPRUc0RExPNEtsd084ZHpYMWwxMnBlUTY2dnJFYVZ2OUFNcXpiZjBUK3ZoTzJ2VWowVERWOVEiLCJtYWMiOiIyY2NjNzliYzk0ZTk2OGU3ZjQzNjk4NzhiOTkxZjJiZmRjZThlMjg0MzA2MTIyNDlkY2QwYWRhYzQxNDZlNTNlIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:46:26 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IkhmNmtZVmhCUXo2QnpSN0d6a3hOeEE9PSIsInZhbHVlIjoia1RUOE9UK09BdmZhNXYyZTkwY2IwQ3VjZG1vYm0xT21LOWJwUnU4M0dHT2xzaDEycUFOY2h3YStZbXpxclM0QnNGMldpRmdCTDU3YjBUUkl5dENzK2oyblRvc0hFaEFiY0t0UDVCcmM4ckVKOFk2M0ZWOEtFUGIveFZodnlPWEEiLCJtYWMiOiI4ZjAxNmJiMGFlOGQ4MjFhYzcwMTI1ZGRjNjYyYTUyNGQzYmQ1MjI2ODA5NTNkZDcyNTNiMjMwOTg5OTFmYWQ2IiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:46:26 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203950066\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2112669296 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U3X7KsSVZAyH5LcE1uJ1vccJjCeU9mfKKbuPehh0</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://goalconversion.gc/gc/lgn/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112669296\", {\"maxDepth\":0})</script>\n"}}