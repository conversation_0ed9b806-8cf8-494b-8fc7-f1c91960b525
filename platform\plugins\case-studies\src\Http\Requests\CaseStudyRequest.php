<?php

namespace Shaqi\CaseStudies\Http\Requests;

use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\Base\Rules\MediaImageRule;
use <PERSON>haqi\Base\Rules\OnOffRule;
use Shaqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CaseStudyRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:400',
            'content' => 'nullable|string',
            'image' => ['nullable', 'string', new MediaImageRule()],
            'client_name' => 'nullable|string|max:255',
            'client_logo' => ['nullable', 'string', new MediaImageRule()],
            'project_url' => 'nullable|url|max:255',
            'challenge' => 'nullable|string',
            'solution' => 'nullable|string',
            'results' => 'nullable|string',
            'technologies' => 'nullable',
            'gallery' => 'nullable',
            'is_featured' => [new OnOffRule()],
            'status' => Rule::in(BaseStatusEnum::values()),
            'categories' => 'nullable|array',
            'categories.*' => 'nullable|integer|exists:case_study_categories,id',
        ];
    }
}
