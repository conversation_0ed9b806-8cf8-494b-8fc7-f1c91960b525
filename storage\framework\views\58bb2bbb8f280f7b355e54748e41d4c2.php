<!-- Case Study Hero Section -->
<div class="case-study-hero" style="background-image: url(<?php echo e($caseStudy->hero_image ?? Theme::asset()->url('img/bg/inner-hero-bg.jpg')); ?>);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 m-auto text-center">
                <div class="case-study-hero-content">
                    <?php if($caseStudy->category): ?>
                    <div class="case-study-category">
                        <span class="category-badge"><?php echo e($caseStudy->category); ?></span>
                    </div>
                    <?php endif; ?>
                    <h1 class="case-study-title text-white"><?php echo e($caseStudy->title ?? 'Case Study Title'); ?></h1>
                    <?php if($caseStudy->subtitle): ?>
                    <p class="case-study-subtitle text-white"><?php echo e($caseStudy->subtitle); ?></p>
                    <?php endif; ?>
                    <div class="case-study-meta">
                        <?php if($caseStudy->client_name): ?>
                        <div class="meta-item">
                            <span class="meta-label">Client:</span>
                            <span class="meta-value"><?php echo e($caseStudy->client_name); ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if($caseStudy->duration): ?>
                        <div class="meta-item">
                            <span class="meta-label">Duration:</span>
                            <span class="meta-value"><?php echo e($caseStudy->duration); ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if($caseStudy->industry): ?>
                        <div class="meta-item">
                            <span class="meta-label">Industry:</span>
                            <span class="meta-value"><?php echo e($caseStudy->industry); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Case Study Content -->
<div class="case-study-content section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Project Overview -->
                <?php if($caseStudy->overview): ?>
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Project Overview</h2>
                    </div>
                    <div class="section-content">
                        <div class="ck-content">
                            <?php echo BaseHelper::clean($caseStudy->overview); ?>

                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Challenge Section -->
                <?php if($caseStudy->challenges || $caseStudy->challenge_description): ?>
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>The Challenge</h2>
                    </div>
                    <div class="section-content">
                        <?php if($caseStudy->challenge_description): ?>
                        <div class="ck-content mb-4">
                            <?php echo BaseHelper::clean($caseStudy->challenge_description); ?>

                        </div>
                        <?php endif; ?>

                        <?php if($caseStudy->challenges && is_array($caseStudy->challenges)): ?>
                        <div class="challenge-grid">
                            <?php $__currentLoopData = $caseStudy->challenges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $challenge): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="challenge-item">
                                <div class="challenge-icon">
                                    <i class="<?php echo e($challenge['icon'] ?? 'fas fa-exclamation-triangle'); ?>"></i>
                                </div>
                                <div class="challenge-content">
                                    <h4><?php echo e($challenge['title'] ?? 'Challenge'); ?></h4>
                                    <p><?php echo e($challenge['description'] ?? ''); ?></p>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Solution Section -->
                <?php if($caseStudy->solutions || $caseStudy->solution_description): ?>
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Our Solution</h2>
                    </div>
                    <div class="section-content">
                        <?php if($caseStudy->solution_description): ?>
                        <div class="ck-content mb-4">
                            <?php echo BaseHelper::clean($caseStudy->solution_description); ?>

                        </div>
                        <?php endif; ?>

                        <?php if($caseStudy->solutions && is_array($caseStudy->solutions)): ?>
                        <div class="solution-timeline">
                            <?php $__currentLoopData = $caseStudy->solutions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $solution): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <span class="timeline-number"><?php echo e($index + 1); ?></span>
                                </div>
                                <div class="timeline-content">
                                    <h4><?php echo e($solution['title'] ?? 'Solution Step'); ?></h4>
                                    <?php if($solution['description']): ?>
                                    <p><?php echo e($solution['description']); ?></p>
                                    <?php endif; ?>
                                    <?php if($solution['features'] && is_array($solution['features'])): ?>
                                    <ul class="solution-list">
                                        <?php $__currentLoopData = $solution['features']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($feature); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Results Section -->
                <?php if($caseStudy->results || $caseStudy->result_description): ?>
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Results & Impact</h2>
                    </div>
                    <div class="section-content">
                        <?php if($caseStudy->result_description): ?>
                        <div class="ck-content mb-4">
                            <?php echo BaseHelper::clean($caseStudy->result_description); ?>

                        </div>
                        <?php endif; ?>

                        <?php if($caseStudy->results && is_array($caseStudy->results)): ?>
                        <div class="results-grid">
                            <?php $__currentLoopData = $caseStudy->results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="result-card">
                                <div class="result-number"><?php echo e($result['number'] ?? '0'); ?></div>
                                <div class="result-label"><?php echo e($result['label'] ?? 'Result'); ?></div>
                                <?php if($result['description']): ?>
                                <div class="result-description"><?php echo e($result['description']); ?></div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php endif; ?>

                        <?php if($caseStudy->chart_image): ?>
                        <div class="results-chart-section">
                            <?php if($caseStudy->chart_title): ?>
                            <h4><?php echo e($caseStudy->chart_title); ?></h4>
                            <?php endif; ?>
                            <div class="chart-placeholder">
                                <img src="<?php echo e(RvMedia::getImageUrl($caseStudy->chart_image)); ?>" alt="<?php echo e($caseStudy->chart_title ?? 'Results Chart'); ?>" class="img-fluid">
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Process & Tools -->
                <?php if($caseStudy->tools || $caseStudy->process_description): ?>
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Process & Tools</h2>
                    </div>
                    <div class="section-content">
                        <?php if($caseStudy->process_description): ?>
                        <div class="ck-content mb-4">
                            <?php echo BaseHelper::clean($caseStudy->process_description); ?>

                        </div>
                        <?php endif; ?>

                        <?php if($caseStudy->tools && is_array($caseStudy->tools)): ?>
                        <div class="tools-grid">
                            <?php $__currentLoopData = $caseStudy->tools; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $toolCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="tool-category">
                                <h4><?php echo e($toolCategory['category'] ?? 'Tools'); ?></h4>
                                <?php if($toolCategory['tools'] && is_array($toolCategory['tools'])): ?>
                                <div class="tool-list">
                                    <?php $__currentLoopData = $toolCategory['tools']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tool): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="tool-item"><?php echo e($tool); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Gallery Section -->
                <?php if($caseStudy->gallery && !empty($caseStudy->gallery)): ?>
                <div class="case-study-section">
                    <div class="heading1">
                        <h2>Project Gallery</h2>
                    </div>
                    <div class="section-content">
                        <div class="gallery-grid">
                            <?php $__currentLoopData = $caseStudy->gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($image): ?>
                            <div class="gallery-item">
                                <img src="<?php echo e(RvMedia::getImageUrl($image)); ?>" alt="<?php echo e($caseStudy->title); ?>" class="img-fluid">
                                <?php if($image['caption'] ?? false): ?>
                                <div class="gallery-caption"><?php echo e($image['caption']); ?></div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Testimonial -->
                <?php if($caseStudy->testimonial_content || $caseStudy->client_testimonial): ?>
                <div class="case-study-section">
                    <div class="case-study-testimonial">
                        <div class="testimonial-content">
                            <div class="testimonial-quote">
                                <i class="fas fa-quote-left"></i>
                            </div>
                            <blockquote>
                                "<?php echo e($caseStudy->testimonial_content ?? $caseStudy->client_testimonial ?? 'Great results achieved through professional service.'); ?>"
                            </blockquote>
                            <div class="testimonial-author">
                                <?php if($caseStudy->client_avatar): ?>
                                <div class="author-image">
                                    <img src="<?php echo e(RvMedia::getImageUrl($caseStudy->client_avatar)); ?>" alt="<?php echo e($caseStudy->client_name ?? 'Client'); ?>">
                                </div>
                                <?php endif; ?>
                                <div class="author-info">
                                    <h5><?php echo e($caseStudy->client_name ?? 'Client Name'); ?></h5>
                                    <?php if($caseStudy->client_position): ?>
                                    <span><?php echo e($caseStudy->client_position); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="case-study-sidebar">
                    <!-- Project Details -->
                    <div class="sidebar-widget">
                        <h3 class="widget-title">Project Details</h3>
                        <div class="project-details">
                            <?php if($caseStudy->client_name): ?>
                            <div class="detail-item">
                                <span class="detail-label">Client:</span>
                                <span class="detail-value"><?php echo e($caseStudy->client_name); ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if($caseStudy->industry): ?>
                            <div class="detail-item">
                                <span class="detail-label">Industry:</span>
                                <span class="detail-value"><?php echo e($caseStudy->industry); ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if($caseStudy->duration): ?>
                            <div class="detail-item">
                                <span class="detail-label">Project Duration:</span>
                                <span class="detail-value"><?php echo e($caseStudy->duration); ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if($caseStudy->team_size): ?>
                            <div class="detail-item">
                                <span class="detail-label">Team Size:</span>
                                <span class="detail-value"><?php echo e($caseStudy->team_size); ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if($caseStudy->budget_range): ?>
                            <div class="detail-item">
                                <span class="detail-label">Budget Range:</span>
                                <span class="detail-value"><?php echo e($caseStudy->budget_range); ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if($caseStudy->services): ?>
                            <div class="detail-item">
                                <span class="detail-label">Services:</span>
                                <span class="detail-value"><?php echo e($caseStudy->services); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Key Metrics -->
                    <?php if($caseStudy->key_metrics && is_array($caseStudy->key_metrics)): ?>
                    <div class="sidebar-widget">
                        <h3 class="widget-title">Key Metrics</h3>
                        <div class="metrics-list">
                            <?php $__currentLoopData = $caseStudy->key_metrics; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $metric): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <i class="<?php echo e($metric['icon'] ?? 'fas fa-chart-line'); ?>"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-number"><?php echo e($metric['number'] ?? '0'); ?></div>
                                    <div class="metric-label"><?php echo e($metric['label'] ?? 'Metric'); ?></div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Related Services -->
                    <div class="sidebar-widget">
                        <h3 class="widget-title">Related Services</h3>
                        <div class="related-services">
                            <a href="#" class="service-link">
                                <i class="fas fa-search"></i>
                                <span>Search Engine Optimization</span>
                            </a>
                            <a href="#" class="service-link">
                                <i class="fas fa-ad"></i>
                                <span>Pay-Per-Click Advertising</span>
                            </a>
                            <a href="#" class="service-link">
                                <i class="fas fa-chart-bar"></i>
                                <span>Conversion Rate Optimization</span>
                            </a>
                            <a href="#" class="service-link">
                                <i class="fas fa-pen"></i>
                                <span>Content Marketing</span>
                            </a>
                            <a href="#" class="service-link">
                                <i class="fas fa-share-alt"></i>
                                <span>Social Media Marketing</span>
                            </a>
                        </div>
                    </div>

                    <!-- Contact CTA -->
                    <div class="sidebar-widget">
                        <div class="cta-widget">
                            <h3>Ready to Grow Your Business?</h3>
                            <p>Get similar results for your company. Let's discuss your project and create a custom strategy.</p>
                            <a href="#" class="theme-btn1 w-100 text-center">Get Free Consultation</a>
                            <div class="cta-contact">
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>+****************</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="case-study-cta section-padding" style="background-color: var(--gc-bg-common-3);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 m-auto text-center">
                <div class="cta-content">
                    <h2 class="cta-title">Ready to Achieve Similar Results?</h2>
                    <p class="cta-description">Join hundreds of successful businesses that have transformed their digital presence with our proven strategies. Let's discuss how we can help you achieve your growth goals.</p>
                    <div class="cta-buttons">
                        <a href="#" class="theme-btn1">Start Your Project</a>
                        <a href="#" class="theme-btn4">View More Case Studies</a>
                    </div>
                    <div class="cta-stats">
                        <div class="stat-item">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Successful Projects</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">98%</div>
                            <div class="stat-label">Client Satisfaction</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">5 Years</div>
                            <div class="stat-label">Average Partnership</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\goalconversion\platform\themes/goalconversion/views/case-study.blade.php ENDPATH**/ ?>