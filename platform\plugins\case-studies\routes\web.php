<?php

use <PERSON><PERSON>qi\Base\Facades\AdminHelper;
use <PERSON>haqi\Theme\Facades\Theme;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Shaqi\CaseStudies\Http\Controllers'], function (): void {
    AdminHelper::registerRoutes(function (): void {
        Route::group(['prefix' => 'case-studies'], function (): void {
            Route::group(['prefix' => 'case-studies', 'as' => 'case-studies.'], function (): void {
                Route::resource('', 'CaseStudyController')
                    ->parameters(['' => 'case-study']);

                Route::get('widgets/recent-case-studies', [
                    'as' => 'widget.recent-case-studies',
                    'uses' => 'CaseStudyController@getWidgetRecentCaseStudies',
                    'permission' => 'case-studies.index',
                ]);
            });

            Route::group(['prefix' => 'categories', 'as' => 'case-study-categories.'], function (): void {
                Route::resource('', 'CaseStudyCategoryController')
                    ->parameters(['' => 'category']);
            });
        });
    });

    Theme::registerRoutes(function (): void {
        Route::get('case-studies', [
            'as' => 'public.case-studies',
            'uses' => 'PublicController@getCaseStudies',
        ]);
    });
});
