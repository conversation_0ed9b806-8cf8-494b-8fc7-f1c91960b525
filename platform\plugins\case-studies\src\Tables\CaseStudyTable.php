<?php

namespace <PERSON><PERSON>qi\CaseStudies\Tables;

use Shaqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\Base\Facades\BaseHelper;
use <PERSON>haqi\Base\Facades\Html;
use <PERSON>haqi\CaseStudies\Models\CaseStudy;
use Shaqi\Table\Abstracts\TableAbstract;
use Shaqi\Table\Actions\DeleteAction;
use Shaqi\Table\Actions\EditAction;
use Shaqi\Table\BulkActions\DeleteBulkAction;
use Shaqi\Table\BulkChanges\CreatedAtBulkChange;
use Shaqi\Table\BulkChanges\NameBulkChange;
use Shaqi\Table\BulkChanges\StatusBulkChange;
use Shaqi\Table\Columns\CreatedAtColumn;
use Shaqi\Table\Columns\FormattedColumn;
use Shaqi\Table\Columns\IdColumn;
use Shaqi\Table\Columns\ImageColumn;
use Shaqi\Table\Columns\NameColumn;
use <PERSON><PERSON>qi\Table\Columns\StatusColumn;
use Shaqi\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class CaseStudyTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(CaseStudy::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('case-studies.create'))
            ->addColumns([
                IdColumn::make(),
                ImageColumn::make(),
                NameColumn::make()->route('case-studies.edit'),
                FormattedColumn::make('categories')
                    ->title(trans('plugins/case-studies::case-studies.categories'))
                    ->orderable(false)
                    ->searchable(false)
                    ->getValueUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        if (! $item->categories->count()) {
                            return '&mdash;';
                        }

                        return Html::tag('span', $item->categories->pluck('name')->implode(', '), [
                            'class' => 'label-success status-label',
                        ])->toHtml();
                    }),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('case-studies.edit'),
                DeleteAction::make()->route('case-studies.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('case-studies.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ]);
    }
}
