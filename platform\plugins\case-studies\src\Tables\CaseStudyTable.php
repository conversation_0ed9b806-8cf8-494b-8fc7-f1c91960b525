<?php

namespace Shaqi\CaseStudies\Tables;

use Shaqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\Base\Facades\BaseHelper;
use <PERSON>haqi\Base\Facades\Html;
use <PERSON>haqi\CaseStudies\Models\CaseStudy;
use Shaqi\CaseStudies\Repositories\Interfaces\CaseStudyInterface;
use Shaqi\Table\Abstracts\TableAbstract;
use Shaqi\Table\Actions\DeleteAction;
use Shaqi\Table\Actions\EditAction;
use Shaqi\Table\BulkActions\DeleteBulkAction;
use Shaqi\Table\Columns\CheckboxColumn;
use Shaqi\Table\Columns\CreatedAtColumn;
use Shaqi\Table\Columns\IdColumn;
use Shaqi\Table\Columns\ImageColumn;
use Shaqi\Table\Columns\NameColumn;
use Shaqi\Table\Columns\StatusColumn;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class CaseStudyTable extends TableAbstract
{
    public function __construct(CaseStudyInterface $repository, UrlGenerator $urlGenerator)
    {
        parent::__construct($repository, $urlGenerator);

        $this->hasActions = true;
        $this->hasFilter = true;
        $this->hasCheckbox = true;
        $this->hasBulkActions = true;
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('name', function (CaseStudy $item) {
                if (! $this->hasPermission('case-studies.edit')) {
                    return BaseHelper::clean($item->name);
                }

                return Html::link(route('case-studies.edit', $item->getKey()), BaseHelper::clean($item->name));
            })
            ->editColumn('image', function (CaseStudy $item) {
                return $this->displayThumbnail($item->image);
            })
            ->editColumn('checkbox', function (CaseStudy $item) {
                return $this->getCheckbox($item->getKey());
            })
            ->editColumn('created_at', function (CaseStudy $item) {
                return BaseHelper::formatDate($item->created_at);
            })
            ->editColumn('status', function (CaseStudy $item) {
                return $item->status->toHtml();
            })
            ->addColumn('operations', function (CaseStudy $item) {
                return $this->getOperations('case-studies.edit', 'case-studies.destroy', $item);
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this->repository->getModel()
            ->select([
                'id',
                'name',
                'image',
                'created_at',
                'status',
                'is_featured',
            ])
            ->with(['categories']);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            ImageColumn::make(),
            NameColumn::make()->route('case-studies.edit'),
            CheckboxColumn::make(),
            CreatedAtColumn::make(),
            StatusColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('case-studies.create'), 'case-studies.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('case-studies.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            'name' => [
                'title' => trans('core/base::tables.name'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'type' => 'datePicker',
            ],
        ];
    }

    public function getFilters(): array
    {
        return $this->getBulkChanges();
    }
}
