<?php

namespace Shaqi\CaseStudies\Forms;

use Shaqi\Base\Forms\FieldOptions\ContentFieldOption;
use Shaqi\Base\Forms\FieldOptions\DescriptionFieldOption;
use Shaqi\Base\Forms\FieldOptions\IsFeaturedFieldOption;
use Shaqi\Base\Forms\FieldOptions\MediaImageFieldOption;
use Shaqi\Base\Forms\FieldOptions\NameFieldOption;
use Shaqi\Base\Forms\FieldOptions\SelectFieldOption;
use Shaqi\Base\Forms\FieldOptions\StatusFieldOption;
use Shaqi\Base\Forms\FieldOptions\MediaImagesFieldOption;
use Shaqi\Base\Forms\FieldOptions\RepeaterFieldOption;
use Shaqi\Base\Forms\Fields\EditorField;
use Shaqi\Base\Forms\Fields\MediaImageField;
use Shaqi\Base\Forms\Fields\OnOffField;
use Shaqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Forms\Fields\TextareaField;
use Shaqi\Base\Forms\Fields\TextField;
use Shaqi\Base\Forms\Fields\TreeCategoryField;
use Shaqi\Base\Forms\Fields\RepeaterField;
use Shaqi\Base\Forms\Fields\MediaImagesField;
use Shaqi\Base\Forms\FormAbstract;
use Shaqi\CaseStudies\Http\Requests\CaseStudyRequest;
use Shaqi\CaseStudies\Models\CaseStudy;
use Shaqi\CaseStudies\Models\CaseStudyCategory;

class CaseStudyForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(CaseStudy::class)
            ->setValidatorClass(CaseStudyRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required())
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add(
                'is_featured',
                OnOffField::class,
                IsFeaturedFieldOption::make()
            )
            ->add(
                'content',
                EditorField::class,
                ContentFieldOption::make()
                    ->allowedShortcodes()
                    ->placeholder(trans('core/base::forms.write_content'))
            )
            ->add(
                'image',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(trans('plugins/case-studies::case-studies.form.image'))
            )
            ->add(
                'client_name',
                TextField::class,
                [
                    'label' => trans('plugins/case-studies::case-studies.form.client_name'),
                    'attr' => ['class' => 'form-control'],
                ]
            )
            ->add(
                'client_logo',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(trans('plugins/case-studies::case-studies.form.client_logo'))
            )
            ->add(
                'project_url',
                TextField::class,
                [
                    'label' => trans('plugins/case-studies::case-studies.form.project_url'),
                    'placeholder' => 'https://example.com',
                    'attr' => ['class' => 'form-control'],
                ]
            )
            ->add(
                'challenge',
                EditorField::class,
                [
                    'label' => trans('plugins/case-studies::case-studies.form.challenge'),
                    'attr' => ['class' => 'form-control'],
                ]
            )
            ->add(
                'solution',
                EditorField::class,
                [
                    'label' => trans('plugins/case-studies::case-studies.form.solution'),
                    'attr' => ['class' => 'form-control'],
                ]
            )
            ->add(
                'results',
                EditorField::class,
                [
                    'label' => trans('plugins/case-studies::case-studies.form.results'),
                    'attr' => ['class' => 'form-control'],
                ]
            )
            ->add(
                'technologies',
                RepeaterField::class,
                RepeaterFieldOption::make()
                    ->label(trans('plugins/case-studies::case-studies.form.technologies'))
                    ->fields([
                        [
                            'type' => 'text',
                            'label' => 'Technology',
                            'attributes' => [
                                'name' => 'name',
                                'value' => null,
                                'options' => [
                                    'class' => 'form-control',
                                    'data-counter' => 255,
                                ],
                            ],
                        ],
                    ])
            )
            ->add(
                'gallery[]',
                MediaImagesField::class,
                MediaImagesFieldOption::make()
                    ->label(trans('plugins/case-studies::case-studies.form.gallery'))
                    ->values($this->getModel()->gallery ?: [])
            )
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->add(
                'categories[]',
                TreeCategoryField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/case-studies::case-studies.form.categories'))
                    ->choices(function () {
                        return CaseStudyCategory::query()
                            ->wherePublished()
                            ->select(['id', 'name', 'parent_id'])
                            ->with('activeChildren')
                            ->where('parent_id', 0)
                            ->get();
                    })
                    ->selected(function () {
                        if ($this->getModel()->getKey()) {
                            return $this->getModel()->categories()->pluck('case_study_categories.id')->toArray();
                        }

                        return [];
                    })
                    ->addAttribute('class', 'list-item-checkbox')
                    ->multiple()
            )
            ->setBreakFieldPoint('status');
    }
}
