{"__meta": {"id": "X5ab4ad174c3ac4f5eb62df46fb53e9aa", "datetime": "2025-07-18 22:46:19", "utime": 1752878779.097369, "method": "GET", "uri": "/gc/lgn/login", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752878773.31639, "end": 1752878779.097417, "duration": 5.78102707862854, "duration_str": "5.78s", "measures": [{"label": "Booting", "start": 1752878773.31639, "relative_start": 0, "end": 1752878774.484425, "relative_end": 1752878774.484425, "duration": 1.1680350303649902, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752878774.484443, "relative_start": 1.1680529117584229, "end": 1752878779.097421, "relative_end": 3.814697265625e-06, "duration": 4.612977981567383, "duration_str": "4.61s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44661288, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 61, "templates": [{"name": "1x core/acl::auth.includes.submit", "param_count": null, "params": [], "start": 1752878775.170925, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/acl/resources/views/auth/includes/submit.blade.phpcore/acl::auth.includes.submit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Facl%2Fresources%2Fviews%2Fauth%2Fincludes%2Fsubmit.blade.php&line=1", "ajax": false, "filename": "submit.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/acl::auth.includes.submit"}, {"name": "1x ********************************::button", "param_count": null, "params": [], "start": 1752878775.278802, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/components/button.blade.php********************************::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::button"}, {"name": "1x __components::8494cc410893d8b166d7e629e68d5c0a", "param_count": null, "params": [], "start": 1752878775.743913, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/8494cc410893d8b166d7e629e68d5c0a.blade.php__components::8494cc410893d8b166d7e629e68d5c0a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F8494cc410893d8b166d7e629e68d5c0a.blade.php&line=1", "ajax": false, "filename": "8494cc410893d8b166d7e629e68d5c0a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8494cc410893d8b166d7e629e68d5c0a"}, {"name": "1x core/acl::auth.form", "param_count": null, "params": [], "start": 1752878776.021552, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/acl/resources/views/auth/form.blade.phpcore/acl::auth.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Facl%2Fresources%2Fviews%2Fauth%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/acl::auth.form"}, {"name": "5x core/base::forms.fields.html", "param_count": null, "params": [], "start": 1752878776.02407, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php&line=1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 5, "name_original": "core/base::forms.fields.html"}, {"name": "8x ********************************::form.field", "param_count": null, "params": [], "start": 1752878776.025996, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 8, "name_original": "********************************::form.field"}, {"name": "8x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": 1752878776.425907, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.partials.help-block"}, {"name": "8x core/base::forms.partials.errors", "param_count": null, "params": [], "start": 1752878776.55232, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.partials.errors"}, {"name": "8x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": 1752878776.68784, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x core/base::forms.fields.text", "param_count": null, "params": [], "start": 1752878776.726172, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.text"}, {"name": "2x core/base::forms.partials.label", "param_count": null, "params": [], "start": 1752878776.904536, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.label"}, {"name": "2x ********************************::form.label", "param_count": null, "params": [], "start": 1752878777.036167, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/components/form/label.blade.php********************************::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::form.label"}, {"name": "1x core/base::forms.fields.password", "param_count": null, "params": [], "start": 1752878777.076961, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/fields/password.blade.phpcore/base::forms.fields.password", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fpassword.blade.php&line=1", "ajax": false, "filename": "password.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.password"}, {"name": "2x __components::57cbaf8e647d5733a928c1a7772d3eaa", "param_count": null, "params": [], "start": 1752878777.091661, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/57cbaf8e647d5733a928c1a7772d3eaa.blade.php__components::57cbaf8e647d5733a928c1a7772d3eaa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2F57cbaf8e647d5733a928c1a7772d3eaa.blade.php&line=1", "ajax": false, "filename": "57cbaf8e647d5733a928c1a7772d3eaa.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::57cbaf8e647d5733a928c1a7772d3eaa"}, {"name": "1x core/base::forms.fields.password-toggle-script", "param_count": null, "params": [], "start": 1752878777.11209, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/fields/password-toggle-script.blade.phpcore/base::forms.fields.password-toggle-script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fpassword-toggle-script.blade.php&line=1", "ajax": false, "filename": "password-toggle-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.password-toggle-script"}, {"name": "1x __components::a805d20b8ab92763767ebc931f22b3c5", "param_count": null, "params": [], "start": 1752878777.123841, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\storage\\framework\\views/a805d20b8ab92763767ebc931f22b3c5.blade.php__components::a805d20b8ab92763767ebc931f22b3c5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fstorage%2Fframework%2Fviews%2Fa805d20b8ab92763767ebc931f22b3c5.blade.php&line=1", "ajax": false, "filename": "a805d20b8ab92763767ebc931f22b3c5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a805d20b8ab92763767ebc931f22b3c5"}, {"name": "1x core/base::forms.fields.checkbox", "param_count": null, "params": [], "start": 1752878777.145032, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/forms/fields/checkbox.blade.phpcore/base::forms.fields.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.checkbox"}, {"name": "1x ********************************::form.checkbox", "param_count": null, "params": [], "start": 1752878777.146725, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.checkbox"}, {"name": "1x core/js-validation::bootstrap", "param_count": null, "params": [], "start": 1752878777.73213, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/acl::layouts.guest", "param_count": null, "params": [], "start": 1752878777.856286, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/acl/resources/views/layouts/guest.blade.phpcore/acl::layouts.guest", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Facl%2Fresources%2Fviews%2Flayouts%2Fguest.blade.php&line=1", "ajax": false, "filename": "guest.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/acl::layouts.guest"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": 1752878777.859221, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x ********************************::layouts.base", "param_count": null, "params": [], "start": 1752878777.933915, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/components/layouts/base.blade.php********************************::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.296976, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.602519, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\vendor\\shaqi\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.787068, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.994268, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\vendor\\shaqi\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}]}, "route": {"uri": "GET gc/lgn/login", "middleware": "web, core, guest", "controller": "Shaqi\\ACL\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "Shaqi\\ACL\\Http\\Controllers", "prefix": "/gc/lgn", "where": [], "as": "access.login", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=29\" onclick=\"\">vendor/shaqi/platform/acl/src/Http/Controllers/Auth/LoginController.php:29-34</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/gc/lgn/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/gc/lgn/login", "status_code": "<pre class=sf-dump id=sf-dump-749834872 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-749834872\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-813199222 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-813199222\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-247018778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-247018778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1783671389 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://goalconversion.gc/gc/lgn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"828 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; XSRF-TOKEN=eyJpdiI6ImtCOFdxdTN4K0pnYWhtV1ZsNGdlcnc9PSIsInZhbHVlIjoiZkNKUHMyM1MxdTNOSjdaOUlNZ2o0cU9TVlFLSkhDYjZKVUJLYTJlc2tsYm9HQy9pblp4V2FjbXR1c2VrazBrRW1LekN6UnpXSnFJeVRjdXN6bUNTZUxJOGxyUUxaZVgxZXhTd0xWV1RpeWZYODRKR0JsOEhxVEtRNmRkRnhBYjUiLCJtYWMiOiIzZTNkM2Y3NzljODM4NzZjMDRiYmZkZTdlZGE2N2RiM2Y1NDBhOWFjMGUyNjQ1ZDQwZTA3NjFjMGRlM2ZmNTMwIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6Ik56dXBwb0xPdEE3eU1vLzA3alFNb1E9PSIsInZhbHVlIjoiVVloVUZvMlF2cEFyM1pwNUN1Z3UvUm1CMmIrY0VxS0QyWUFKS29yZ0JtVFJjSzNDYVlnYnZVanBXMTVBNkp4a2xoMFBxRXJRVjk0MTNDUGlONXpEZUxEa1RPWHRLMFduMkEvUVJya3lWcHJVN3RXQTJNOGdiQ044RGcrdkxDZHQiLCJtYWMiOiJkZmQxZTdmYWM1MDljMDgzNTA4MWNmNDc0YmQ0ZTkyNDIxZjM2MGE4Y2E4YTU0MWE3NWI3NGIzNjFlNTFiNTllIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752869141$o43$g1$t1752874702$j60$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783671389\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-48581571 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mHLAZjM1JlnDMgXjdTz81tKLTfRvwSGOZHTC25kf</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48581571\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1452971213 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 22:46:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6ImRJMm1LRGI1Zi96WVpOZUQ0aWpzeXc9PSIsInZhbHVlIjoiMlBKQjdTS0Y0N2JkNjZscnk2VFM3QzN3RGhsakpic0V0MCtPbEhaWWhMZGFCaDQ4NEE2TGE2Ujlnd28vR2hVOWVlam1IQThwRlY3S2crNnozelN0dVNMZUNWYVZqYWIrbktxcjhHa1A2U0lJZWo2Y0pvZDJ2REtCdURTVExFRWMiLCJtYWMiOiJmNTZkMjlkY2Y2ODZmZjI4NzE0NGZjZDMxYWZmYTc1MWFjZmEyNmYwYTM3MjdmNzViZDNkZGI2NGJjOWRkMDQwIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:46:19 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6IkxyQThGei9nWHBWVEhpYjJSV0VzMWc9PSIsInZhbHVlIjoidUhOcWdUMVRTWStBakl2bnJyM3JSQnk2WFZDK2t4WDVPNGxZWldvOGZxNTNVQWl4REtYUnM0VU9xRElvR25rK1FTeW1hbW9DWHhvK0VuL1c1U2JKM00vOVV3d1F1V2w2SXBQcW9wQ1VRQVlqZ3BwZGdQSUs3aEVsVmduV1pFQ2QiLCJtYWMiOiIwMWI3YmE3NjMwY2Y5ZmZjYTc1ODU1Nzg1NWQwZGM0MGE4YzVmM2FhOTAxYzkyYjhlYzJkMTliM2RiY2FmYjMzIiwidGFnIjoiIn0%3D; expires=Sat, 19 Jul 2025 00:46:19 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6ImRJMm1LRGI1Zi96WVpOZUQ0aWpzeXc9PSIsInZhbHVlIjoiMlBKQjdTS0Y0N2JkNjZscnk2VFM3QzN3RGhsakpic0V0MCtPbEhaWWhMZGFCaDQ4NEE2TGE2Ujlnd28vR2hVOWVlam1IQThwRlY3S2crNnozelN0dVNMZUNWYVZqYWIrbktxcjhHa1A2U0lJZWo2Y0pvZDJ2REtCdURTVExFRWMiLCJtYWMiOiJmNTZkMjlkY2Y2ODZmZjI4NzE0NGZjZDMxYWZmYTc1MWFjZmEyNmYwYTM3MjdmNzViZDNkZGI2NGJjOWRkMDQwIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:46:19 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6IkxyQThGei9nWHBWVEhpYjJSV0VzMWc9PSIsInZhbHVlIjoidUhOcWdUMVRTWStBakl2bnJyM3JSQnk2WFZDK2t4WDVPNGxZWldvOGZxNTNVQWl4REtYUnM0VU9xRElvR25rK1FTeW1hbW9DWHhvK0VuL1c1U2JKM00vOVV3d1F1V2w2SXBQcW9wQ1VRQVlqZ3BwZGdQSUs3aEVsVmduV1pFQ2QiLCJtYWMiOiIwMWI3YmE3NjMwY2Y5ZmZjYTc1ODU1Nzg1NWQwZGM0MGE4YzVmM2FhOTAxYzkyYjhlYzJkMTliM2RiY2FmYjMzIiwidGFnIjoiIn0%3D; expires=Sat, 19-Jul-2025 00:46:19 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452971213\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1510425886 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://goalconversion.gc/gc/lgn/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510425886\", {\"maxDepth\":0})</script>\n"}}