@use '../../utils/' as * ;

/*
 ::::::::::::::::::::::::::
  CASE STUDY DETAIL PAGE CSS
 ::::::::::::::::::::::::::
 */

// ==== Case Study Hero Section ====
.case-study-hero {
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 120px 0 80px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(9, 11, 14, 0.7);
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .case-study-hero-content {
    text-align: center;

    .case-study-category {
      margin-bottom: 20px;

      .category-badge {
        display: inline-block;
        background: var(--gc-bg-main);
        color: var(--gc-text-white);
        padding: 8px 20px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .case-study-title {
      font-size: 48px;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 20px;

      @media #{$lg} {
        font-size: 42px;
      }

      @media #{$md} {
        font-size: 36px;
      }

      @media #{$sm} {
        font-size: 30px;
      }
    }

    .case-study-subtitle {
      font-size: 20px;
      line-height: 1.6;
      margin-bottom: 40px;
      opacity: 0.9;

      @media #{$md} {
        font-size: 18px;
      }
    }

    .case-study-meta {
      display: flex;
      justify-content: center;
      gap: 40px;
      flex-wrap: wrap;

      @media #{$sm} {
        gap: 20px;
      }

      .meta-item {
        text-align: center;

        .meta-label {
          display: block;
          font-size: 14px;
          opacity: 0.8;
          margin-bottom: 5px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .meta-value {
          display: block;
          font-size: 18px;
          font-weight: 600;
        }
      }
    }
  }
}

// ==== Case Study Content ====
.case-study-content {
  .case-study-section {
    margin-bottom: 60px;

    &:last-child {
      margin-bottom: 0;
    }

    // Using theme's heading1 class instead of custom section-header

    .section-content {
      .lead-text {
        font-size: 20px;
        line-height: 1.7;
        color: var(--gc-text-title1);
        font-weight: 500;
        margin-bottom: 20px;

        @media #{$md} {
          font-size: 18px;
        }
      }

      p {
        font-size: 16px;
        line-height: 1.7;
        color: var(--gc-text-pera1);
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// ==== Challenge Grid ====
.challenge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 30px;

  @media #{$sm} {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .challenge-item {
    background: var(--gc-bg-white);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(9, 11, 14, 0.08);
    border: 1px solid var(--gc-border-1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(9, 11, 14, 0.12);
    }

    .challenge-icon {
      width: 60px;
      height: 60px;
      background: var(--gc-bg-common-11);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;

      i {
        font-size: 24px;
        color: var(--gc-bg-main);
      }
    }

    .challenge-content {
      h4 {
        font-size: 20px;
        font-weight: 600;
        color: var(--gc-text-title1);
        margin-bottom: 12px;
      }

      p {
        font-size: 15px;
        line-height: 1.6;
        color: var(--gc-text-pera1);
        margin: 0;
      }
    }
  }
}

// ==== Solution Timeline ====
.solution-timeline {
  position: relative;
  padding-left: 40px;

  @media #{$sm} {
    padding-left: 30px;
  }

  &::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gc-border-1);

    @media #{$sm} {
      left: 15px;
    }
  }

  .timeline-item {
    position: relative;
    margin-bottom: 40px;

    &:last-child {
      margin-bottom: 0;
    }

    .timeline-marker {
      position: absolute;
      left: -30px;
      top: 0;
      width: 40px;
      height: 40px;
      background: var(--gc-bg-main);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;

      @media #{$sm} {
        left: -25px;
        width: 30px;
        height: 30px;
      }

      .timeline-number {
        color: var(--gc-text-white);
        font-weight: 700;
        font-size: 16px;

        @media #{$sm} {
          font-size: 14px;
        }
      }
    }

    .timeline-content {
      background: var(--gc-bg-white);
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 5px 20px rgba(9, 11, 14, 0.08);
      border: 1px solid var(--gc-border-1);

      @media #{$sm} {
        padding: 20px;
      }

      h4 {
        font-size: 22px;
        font-weight: 600;
        color: var(--gc-text-title1);
        margin-bottom: 15px;

        @media #{$sm} {
          font-size: 20px;
        }
      }

      p {
        font-size: 16px;
        line-height: 1.6;
        color: var(--gc-text-pera1);
        margin-bottom: 20px;
      }

      .solution-list {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          position: relative;
          padding-left: 25px;
          margin-bottom: 10px;
          font-size: 15px;
          line-height: 1.6;
          color: var(--gc-text-pera1);

          &:last-child {
            margin-bottom: 0;
          }

          &::before {
            content: '✓';
            position: absolute;
            left: 0;
            top: 0;
            color: var(--gc-bg-main);
            font-weight: 700;
          }
        }
      }
    }
  }
}

// ==== Results Grid ====
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 50px;

  @media #{$sm} {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  @media #{$xs} {
    grid-template-columns: 1fr;
  }

  .result-card {
    background: var(--gc-bg-white);
    padding: 40px 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(9, 11, 14, 0.08);
    border: 1px solid var(--gc-border-1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(9, 11, 14, 0.12);
    }

    @media #{$sm} {
      padding: 30px 20px;
    }

    .result-number {
      font-size: 48px;
      font-weight: 900;
      color: var(--gc-bg-main);
      line-height: 1;
      margin-bottom: 10px;

      @media #{$sm} {
        font-size: 36px;
      }
    }

    .result-label {
      font-size: 18px;
      font-weight: 600;
      color: var(--gc-text-title1);
      margin-bottom: 10px;

      @media #{$sm} {
        font-size: 16px;
      }
    }

    .result-description {
      font-size: 14px;
      line-height: 1.5;
      color: var(--gc-text-pera1);
      margin: 0;
    }
  }
}

.results-chart-section {
  margin-top: 40px;

  h4 {
    font-size: 24px;
    font-weight: 600;
    color: var(--gc-text-title1);
    margin-bottom: 20px;
    text-align: center;
  }

  .chart-placeholder {
    background: var(--gc-bg-common-3);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(9, 11, 14, 0.08);

    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }
}

// ==== Tools Grid ====
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;

  @media #{$sm} {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .tool-category {
    background: var(--gc-bg-white);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(9, 11, 14, 0.08);
    border: 1px solid var(--gc-border-1);

    @media #{$sm} {
      padding: 20px;
    }

    h4 {
      font-size: 20px;
      font-weight: 600;
      color: var(--gc-text-title1);
      margin-bottom: 20px;

      @media #{$sm} {
        font-size: 18px;
      }
    }

    .tool-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .tool-item {
        display: inline-block;
        background: var(--gc-bg-common-3);
        color: var(--gc-text-title1);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          background: var(--gc-bg-main);
          color: var(--gc-text-white);
        }
      }
    }
  }
}

// ==== Gallery Grid ====
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;

  @media #{$sm} {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .gallery-item {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(9, 11, 14, 0.08);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(9, 11, 14, 0.12);
    }

    img {
      width: 100%;
      height: 200px;
      object-fit: cover;
      display: block;
    }

    .gallery-caption {
      padding: 20px;
      background: var(--gc-bg-white);
      font-size: 16px;
      font-weight: 500;
      color: var(--gc-text-title1);
      text-align: center;
    }
  }
}

// ==== Case Study Testimonial ====
.case-study-testimonial {
  background: var(--gc-bg-common-3);
  border-radius: 12px;
  padding: 50px 40px;
  text-align: center;

  @media #{$sm} {
    padding: 30px 20px;
  }

  .testimonial-content {
    .testimonial-quote {
      margin-bottom: 30px;

      i {
        font-size: 48px;
        color: var(--gc-bg-main);
        opacity: 0.3;
      }
    }

    blockquote {
      font-size: 24px;
      line-height: 1.6;
      color: var(--gc-text-title1);
      font-style: italic;
      margin-bottom: 40px;
      font-weight: 500;

      @media #{$md} {
        font-size: 20px;
      }

      @media #{$sm} {
        font-size: 18px;
      }
    }

    .testimonial-author {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;

      @media #{$sm} {
        flex-direction: column;
        gap: 15px;
      }

      .author-image {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .author-info {
        text-align: left;

        @media #{$sm} {
          text-align: center;
        }

        h5 {
          font-size: 20px;
          font-weight: 600;
          color: var(--gc-text-title1);
          margin-bottom: 5px;
        }

        span {
          font-size: 16px;
          color: var(--gc-text-pera1);
        }
      }
    }
  }
}

// ==== Case Study Sidebar ====
.case-study-sidebar {
  .sidebar-widget {
    background: var(--gc-bg-white);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 20px rgba(9, 11, 14, 0.08);
    border: 1px solid var(--gc-border-1);

    &:last-child {
      margin-bottom: 0;
    }

    @media #{$sm} {
      padding: 20px;
    }

    .widget-title {
      font-size: 22px;
      font-weight: 600;
      color: var(--gc-text-title1);
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 2px solid var(--gc-bg-common-3);

      @media #{$sm} {
        font-size: 20px;
      }
    }
  }
}

// Project Details
.project-details {
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid var(--gc-border-1);

    &:last-child {
      border-bottom: none;
    }

    .detail-label {
      font-weight: 600;
      color: var(--gc-text-title1);
      font-size: 15px;
      flex-shrink: 0;
      margin-right: 15px;
    }

    .detail-value {
      color: var(--gc-text-pera1);
      font-size: 15px;
      text-align: right;
      line-height: 1.4;
    }
  }
}

// Metrics List
.metrics-list {
  .metric-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--gc-border-1);

    &:last-child {
      border-bottom: none;
    }

    .metric-icon {
      width: 50px;
      height: 50px;
      background: var(--gc-bg-common-11);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 20px;
        color: var(--gc-bg-main);
      }
    }

    .metric-content {
      .metric-number {
        font-size: 24px;
        font-weight: 700;
        color: var(--gc-bg-main);
        line-height: 1;
        margin-bottom: 5px;
      }

      .metric-label {
        font-size: 14px;
        color: var(--gc-text-pera1);
        line-height: 1;
      }
    }
  }
}

// Related Services
.related-services {
  .service-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--gc-border-1);
    text-decoration: none;
    color: var(--gc-text-title1);
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      color: var(--gc-bg-main);
      padding-left: 10px;
    }

    i {
      width: 20px;
      font-size: 16px;
      color: var(--gc-bg-main);
    }

    span {
      font-size: 15px;
      font-weight: 500;
    }
  }
}

// CTA Widget
.cta-widget {
  text-align: center;

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--gc-text-title1);
    margin-bottom: 15px;
  }

  p {
    font-size: 15px;
    line-height: 1.6;
    color: var(--gc-text-pera1);
    margin-bottom: 25px;
  }

  .cta-contact {
    margin-top: 20px;

    .contact-item {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      margin-bottom: 10px;
      font-size: 14px;
      color: var(--gc-text-pera1);

      &:last-child {
        margin-bottom: 0;
      }

      i {
        color: var(--gc-bg-main);
        width: 16px;
      }
    }
  }
}

// ==== Case Study CTA Section ====
.case-study-cta {
  .cta-content {
    .cta-title {
      font-size: 42px;
      font-weight: 700;
      color: var(--gc-text-title1);
      margin-bottom: 20px;

      @media #{$lg} {
        font-size: 36px;
      }

      @media #{$md} {
        font-size: 32px;
      }

      @media #{$sm} {
        font-size: 28px;
      }
    }

    .cta-description {
      font-size: 18px;
      line-height: 1.6;
      color: var(--gc-text-pera1);
      margin-bottom: 40px;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;

      @media #{$md} {
        font-size: 16px;
      }
    }

    .cta-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 50px;

      @media #{$sm} {
        flex-direction: column;
        align-items: center;
        gap: 15px;
      }
    }

    .cta-stats {
      display: flex;
      justify-content: center;
      gap: 60px;

      @media #{$md} {
        gap: 40px;
      }

      @media #{$sm} {
        flex-direction: column;
        gap: 30px;
      }

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 36px;
          font-weight: 900;
          color: var(--gc-bg-main);
          line-height: 1;
          margin-bottom: 8px;

          @media #{$sm} {
            font-size: 30px;
          }
        }

        .stat-label {
          font-size: 16px;
          color: var(--gc-text-pera1);
          font-weight: 500;

          @media #{$sm} {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// ==== Responsive Adjustments ====
@media #{$lg} {
  .case-study-hero {
    padding: 100px 0 60px;
  }

  .case-study-content {
    .case-study-section {
      margin-bottom: 50px;
    }
  }
}

@media #{$md} {
  .case-study-hero {
    padding: 80px 0 50px;
  }

  .case-study-content {
    .case-study-section {
      margin-bottom: 40px;
    }
  }

  .case-study-sidebar {
    margin-top: 40px;
  }
}

@media #{$sm} {
  .case-study-hero {
    padding: 60px 0 40px;
  }

  .case-study-content {
    .case-study-section {
      margin-bottom: 30px;
    }
  }
}
