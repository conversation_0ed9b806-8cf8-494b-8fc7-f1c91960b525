{"__meta": {"id": "X79c27edaf63282a0aba408938c5845e0", "datetime": "2025-07-18 21:03:17", "utime": **********.394681, "method": "GET", "uri": "/testimonials", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.348676, "end": **********.3947, "duration": 1.0460240840911865, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": **********.348676, "relative_start": 0, "end": **********.968002, "relative_end": **********.968002, "duration": 0.619326114654541, "duration_str": "619ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.968024, "relative_start": 0.6193480491638184, "end": **********.394702, "relative_end": 1.9073486328125e-06, "duration": 0.426677942276001, "duration_str": "427ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45213840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "theme.goalconversion::views.page", "param_count": null, "params": [], "start": **********.028033, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/views/page.blade.phptheme.goalconversion::views.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fviews%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}}, {"name": "theme.goalconversion::layouts.testimonials", "param_count": null, "params": [], "start": **********.030539, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.phptheme.goalconversion::layouts.testimonials", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Flayouts%2Ftestimonials.blade.php&line=1", "ajax": false, "filename": "testimonials.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.header", "param_count": null, "params": [], "start": **********.031169, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/header.blade.phptheme.goalconversion::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "packages/theme::partials.header", "param_count": null, "params": [], "start": **********.032621, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.footer", "param_count": null, "params": [], "start": **********.08993, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/footer.blade.phptheme.goalconversion::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.389179, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET {slug?}", "middleware": "web, core", "controller": "Shaqi\\Theme\\Http\\Controllers\\PublicController@getView", "namespace": null, "prefix": "", "where": [], "as": "public.single", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php&line=44\" onclick=\"\">vendor/shaqi/theme/src/Http/Controllers/PublicController.php:44-93</a>"}, "queries": {"nb_statements": 14, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0061200000000000004, "accumulated_duration_str": "6.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `slugs` where (`key` = 'testimonials' and `prefix` = '') limit 1", "type": "query", "params": [], "bindings": ["testimonials", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/slug/src/SlugHelper.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\SlugHelper.php", "line": 182}, {"index": 19, "namespace": null, "name": "vendor/shaqi/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.999897, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 8.497}, {"sql": "select * from `pages` where (`id` = 19 and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": [19, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Services\\PageService.php", "line": 37}, {"index": 18, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.006992, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 8.497, "width_percent": 7.843}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (19) and `slugs`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/shaqi/page/src/Services/PageService.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Services\\PageService.php", "line": 37}, {"index": 24, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.011771, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 16.34, "width_percent": 5.556}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (19) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "vendor/shaqi/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 87}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 30, "namespace": null, "name": "vendor/shaqi/page/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 142}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.02092, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 21.895, "width_percent": 8.333}, {"sql": "select * from `testimonials` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.055925, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 30.229, "width_percent": 12.582}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'review_platform_logo' and `reference_id` = 1 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["review_platform_logo", 1, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.058429, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 42.81, "width_percent": 8.007}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'company_logo' and `reference_id` = 1 and `reference_type` = '<PERSON>haqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["company_logo", 1, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 45}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.060477, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 50.817, "width_percent": 5.392}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'testimonial_title' and `reference_id` = 1 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["testimonial_title", 1, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0621362, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 56.209, "width_percent": 4.739}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'review_platform_logo' and `reference_id` = 2 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["review_platform_logo", 2, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.073695, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 60.948, "width_percent": 8.66}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'company_logo' and `reference_id` = 2 and `reference_type` = '<PERSON>haqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["company_logo", 2, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 45}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0756109, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 69.608, "width_percent": 6.209}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'testimonial_title' and `reference_id` = 2 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["testimonial_title", 2, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.077278, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 75.817, "width_percent": 6.046}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'review_platform_logo' and `reference_id` = 3 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["review_platform_logo", 3, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.081026, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 81.863, "width_percent": 5.882}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'company_logo' and `reference_id` = 3 and `reference_type` = '<PERSON>haqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["company_logo", 3, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 45}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0827801, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 87.745, "width_percent": 5.392}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'testimonial_title' and `reference_id` = 3 and `reference_type` = 'Shaqi\\\\Testimonial\\\\Models\\\\Testimonial') limit 1", "type": "query", "params": [], "bindings": ["testimonial_title", 3, "<PERSON><PERSON>qi\\Testimonial\\Models\\Testimonial"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "vendor/shaqi/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": "view", "name": "theme.goalconversion::layouts.testimonials", "file": "D:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/testimonials.blade.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0844078, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 93.137, "width_percent": 6.863}]}, "models": {"data": {"Shaqi\\Base\\Models\\MetaBox": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Shaqi\\Testimonial\\Models\\Testimonial": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fplugins%2Ftestimonial%2Fsrc%2FModels%2FTestimonial.php&line=1", "ajax": false, "filename": "Testimonial.php", "line": "?"}}, "Shaqi\\Slug\\Models\\Slug": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Shaqi\\Page\\Models\\Page": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fpage%2Fsrc%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}}, "count": 17, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/testimonials\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/testimonials", "status_code": "<pre class=sf-dump id=sf-dump-338318761 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-338318761\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1624724656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1624724656\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1574805875 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1574805875\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-883244564 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">https://goalconversion.gc/portfolio</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"828 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; XSRF-TOKEN=eyJpdiI6InlRQ0J6ajhmb2EzREt6anNOa2JET3c9PSIsInZhbHVlIjoiT0lTMVVMWW5lVU1TSzJLVXIvWUpxOEt2TEc4Y1FLUklPaGwzSVRCZW9lcWdaYjFxQ2djVjRJNDYzU3Q1YllIY2R1OUlZeVBPbGMzaEF4ckVmd21hYkxEeStkY05XdERJZmcyTUpoWFlNZUoxVkVLN1RQV0kyd1J2cTd6M2R0RWoiLCJtYWMiOiI3MDRkN2QwMTBmYWUwMmQzZTVlMDlmNjI2N2RiMjk3ZWQ3M2Y1ZjAzZmYxNTZlOTMxOTIxNjUzMmE1NjIwYjRhIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IkthdmJXa0F0TWdkeGY1dHRHcmlxNEE9PSIsInZhbHVlIjoiVnRDRHZRb2o5UE5vZlk1ek9jWWhKREt2TzlaeUQ5OEVXYTFDbzJ1S2k5MTl0ZUxJZmNTNGQ3Y2lqZkZmR0pNOGRvNnpna1ZEVGZTWWZuL1JHWjI5QUVKVHdRQXVCL2VUdTNjODI1eGcvdktHL3dnM3RNTzFDeUkvNVprVG1GYnYiLCJtYWMiOiI5ZWZlM2NkNTFlYmQyOGYwYzRkODBjOGRiNDBkZTA3OGMwM2E0M2E5MDJmNzJkNzFiYzRiZmI4YjJjODk0YjdmIiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1752869141$o43$g1$t1752872547$j27$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883244564\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1810648361 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mHLAZjM1JlnDMgXjdTz81tKLTfRvwSGOZHTC25kf</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810648361\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1746427641 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 21:03:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cms-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.5.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization-at</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>activated-license</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">Yes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IlpYNGRCaXl3MXVYZDdiUDlNUUJJdnc9PSIsInZhbHVlIjoieE1MeWxseXBOWjIrUGNZK2pEQ2JEQ2xVQ3REMk14M1FBZ3VPYXJqUkdIK0pYcGdBZy9OekNqMXlDenE4QTl4R29UZHYyTzRYNFQ2WGpmb0RrVXhsWTcwKzAwTWNROTcrK0Q2YVFORHlVdVhadXp6WE53VWh6WGRjOG9yRTJBdXYiLCJtYWMiOiJlMDUwMDdkMDRhNzlmYTZlMGYwMjZlNmQyYzI5NmE4OGY1Zjk1Mjk1MTEzNjk4YTkzYjFiZTIxOTZlZWQ4NDRkIiwidGFnIjoiIn0%3D; expires=Fri, 18 Jul 2025 23:03:17 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6Ill3YXRGYTA3NXRJRklid01yK0xkMXc9PSIsInZhbHVlIjoiVVd3bXJtWlJuS0Nsd2w1OTlGTlVpQVB2aWhWY3hKTHdyWlRmMGhEb2RoelVVc0hvSkJVOUUydE5rbzIrbTJQWGkyVnlsckxPaVBZc21oTHBrUWo3aDg1NWFkVzRxUDBpN2JoaXEzTE5Oa1NoMHUzcVNIRE83RzVhenlRZzBBMVMiLCJtYWMiOiI4OWI0NDc4Nzk4YWYwZWFhY2U2NTMxMTY2OTlkOWQwZDI2YzZjZjQ3YjZkMDdjNjU5YzBmNjZlNTA0MmJjMmM3IiwidGFnIjoiIn0%3D; expires=Fri, 18 Jul 2025 23:03:17 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IlpYNGRCaXl3MXVYZDdiUDlNUUJJdnc9PSIsInZhbHVlIjoieE1MeWxseXBOWjIrUGNZK2pEQ2JEQ2xVQ3REMk14M1FBZ3VPYXJqUkdIK0pYcGdBZy9OekNqMXlDenE4QTl4R29UZHYyTzRYNFQ2WGpmb0RrVXhsWTcwKzAwTWNROTcrK0Q2YVFORHlVdVhadXp6WE53VWh6WGRjOG9yRTJBdXYiLCJtYWMiOiJlMDUwMDdkMDRhNzlmYTZlMGYwMjZlNmQyYzI5NmE4OGY1Zjk1Mjk1MTEzNjk4YTkzYjFiZTIxOTZlZWQ4NDRkIiwidGFnIjoiIn0%3D; expires=Fri, 18-Jul-2025 23:03:17 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6Ill3YXRGYTA3NXRJRklid01yK0xkMXc9PSIsInZhbHVlIjoiVVd3bXJtWlJuS0Nsd2w1OTlGTlVpQVB2aWhWY3hKTHdyWlRmMGhEb2RoelVVc0hvSkJVOUUydE5rbzIrbTJQWGkyVnlsckxPaVBZc21oTHBrUWo3aDg1NWFkVzRxUDBpN2JoaXEzTE5Oa1NoMHUzcVNIRE83RzVhenlRZzBBMVMiLCJtYWMiOiI4OWI0NDc4Nzk4YWYwZWFhY2U2NTMxMTY2OTlkOWQwZDI2YzZjZjQ3YjZkMDdjNjU5YzBmNjZlNTA0MmJjMmM3IiwidGFnIjoiIn0%3D; expires=Fri, 18-Jul-2025 23:03:17 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746427641\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-548957138 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RlptUJP3BmRjvcv3zYBf1G7izo7lgaHnupTzDt1R</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://goalconversion.gc/testimonials</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548957138\", {\"maxDepth\":0})</script>\n"}}